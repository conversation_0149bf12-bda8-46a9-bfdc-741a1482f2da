#!/usr/bin/env python3

import sys
import os
import json
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lib.datasets.linemod import inout

def debug_split3_data():
    print("=== Debug Split3 Data Loading ===")
    
    # 1. 检查split3的划分
    print("\n1. Split3 划分:")
    seen_id_obj, seen_names, seen_occ_id_obj, seen_occ_names, unseen_id_obj, unseen_names, unseen_occ_id_obj, unseen_occ_names = inout.get_list_id_obj_from_split_name('split3')
    
    print(f"unseen_occ_id_obj: {unseen_occ_id_obj}")
    print(f"unseen_occ_names: {unseen_occ_names}")
    
    # 2. 检查occlusionLINEMOD.json中的数据
    print("\n2. occlusionLINEMOD.json 数据:")
    json_path = "dataset/occlusionLINEMOD.json"
    
    if not os.path.exists(json_path):
        print(f"ERROR: {json_path} 不存在!")
        return
    
    with open(json_path) as json_file:
        query_frame = json.load(json_file)
    
    query_frame = pd.DataFrame.from_dict(query_frame, orient='index')
    query_frame = query_frame.transpose()
    
    available_ids = sorted(query_frame['id_obj'].unique())
    print(f"JSON中可用的物体ID: {available_ids}")
    
    # 3. 检查split3 unseen_occ的数据是否存在
    print(f"\n3. Split3 unseen_occ 数据检查:")
    print(f"需要的ID: {list(unseen_occ_id_obj)}")
    
    for obj_id in unseen_occ_id_obj:
        count = len(query_frame[query_frame['id_obj'] == obj_id])
        print(f"ID {obj_id}: {count} 条记录")
        
        if count > 0:
            # 显示前几条记录的路径
            sample_data = query_frame[query_frame['id_obj'] == obj_id].head(3)
            print(f"  样本路径:")
            for idx, row in sample_data.iterrows():
                print(f"    {row['real_path']}")
    
    # 4. 检查过滤后的数据
    print(f"\n4. 过滤后的数据:")
    filtered_frame = query_frame[query_frame.id_obj.isin(unseen_occ_id_obj)]
    print(f"过滤后数据长度: {len(filtered_frame)}")
    
    if len(filtered_frame) == 0:
        print("ERROR: 过滤后数据为空!")
        print("这就是为什么unseen_occ_test错误率和准确率都为0的原因!")
    else:
        print("数据过滤正常")
        
    # 5. 检查图像文件是否存在
    print(f"\n5. 图像文件检查:")
    if len(filtered_frame) > 0:
        sample_row = filtered_frame.iloc[0]
        rgb_path = sample_row['real_path']
        full_path = f"dataset/crop_image512/occlusionLINEMOD/{rgb_path}"
        mask_path = full_path.replace('.png', '_mask.png')
        
        print(f"样本图像路径: {full_path}")
        print(f"图像文件存在: {os.path.exists(full_path)}")
        print(f"Mask文件存在: {os.path.exists(mask_path)}")

if __name__ == "__main__":
    debug_split3_data()
