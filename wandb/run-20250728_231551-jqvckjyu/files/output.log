特征提取器可训练参数数量: 2.36M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.13M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.13M
初始阶段优化器参数数量: 2.36M
  0%|                                                                                                                                           | 0/623 [00:43<?, ?it/s]

🔄 Epoch 0: 切换到Hash训练阶段（实验1：简化版本）
   🎯 目标：复现wrap版本的训练方式
   📝 配置：只使用Hash特征损失 + Hash正则化损失
   📊 简化训练参数统计:
      - 主要模型: 2.36M
      - Hash编码器: 0.13M
      - 总计: 2.50M
   📈 统一学习率: 0.0001
   ✅ 优化器已切换到简化Hash训练模式（等价wrap版本）
✅ Hash编码器已设置为训练模式
  0%|                                                                                                                                            | 0/25 [00:44<?, ?it/s]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
Traceback (most recent call last):
  File "train_new.py", line 437, in <module>
    main()
  File "train_new.py", line 322, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 173, in train
    neg_features = model(neg)  # 返回字典
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 1372, in forward
    sam_cls_tokens, sam_patch_tokens = self.extract_sam_features(x, sam_layer_indices)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 1321, in extract_sam_features
    _ = self.sam_encoder.image_encoder(x_resized)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 254, in forward
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 138, in forward
    x = x + self.attn(self.norm1(x))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 82, in forward
    attn = (q @ k.transpose(-2, -1)) * self.scale
RuntimeError: CUDA out of memory. Tried to allocate 3.00 GiB (GPU 0; 23.64 GiB total capacity; 9.04 GiB already allocated; 1.52 GiB free; 12.01 GiB reserved in total by PyTorch) If reserved memory is >> allocated memory try setting max_split_size_mb to avoid fragmentation.  See documentation for Memory Management and PYTORCH_CUDA_ALLOC_CONF
