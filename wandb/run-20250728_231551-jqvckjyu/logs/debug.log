2025-07-28 23:15:51,676 INFO    MainThread:3195465 [wandb_setup.py:_flush():67] Current SDK version is 0.19.8
2025-07-28 23:15:51,677 INFO    MainThread:3195465 [wandb_setup.py:_flush():67] Configure stats pid to 3195465
2025-07-28 23:15:51,677 INFO    MainThread:3195465 [wandb_setup.py:_flush():67] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-28 23:15:51,677 INFO    MainThread:3195465 [wandb_setup.py:_flush():67] Loading settings from /home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/settings
2025-07-28 23:15:51,677 INFO    MainThread:3195465 [wandb_setup.py:_flush():67] Loading settings from environment variables
2025-07-28 23:15:51,677 INFO    MainThread:3195465 [wandb_init.py:setup_run_log_directory():647] Logging user logs to /home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250728_231551-jqvckjyu/logs/debug.log
2025-07-28 23:15:51,678 INFO    MainThread:3195465 [wandb_init.py:setup_run_log_directory():648] Logging internal logs to /home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250728_231551-jqvckjyu/logs/debug-internal.log
2025-07-28 23:15:51,678 INFO    MainThread:3195465 [wandb_init.py:init():761] calling init triggers
2025-07-28 23:15:51,678 INFO    MainThread:3195465 [wandb_init.py:init():766] wandb.init called with sweep_config: {}
config: {'learning_rate': 0.0001, 'batch_size': 16, 'epochs': 25, 'weight_decay': 0.0005, 'descriptor_size': 128, 'split': 'split1', 'feature_blocks': [9, 10, 11, 12], 'offset_prediction_enabled': False, 'offset_loss_weight': 0, '_wandb': {'code_path': 'code/train_new.py'}}
2025-07-28 23:15:51,678 INFO    MainThread:3195465 [wandb_init.py:init():784] starting backend
2025-07-28 23:15:51,678 INFO    MainThread:3195465 [wandb_init.py:init():788] sending inform_init request
2025-07-28 23:15:51,747 INFO    MainThread:3195465 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-07-28 23:15:51,747 INFO    MainThread:3195465 [wandb_init.py:init():798] backend started and connected
2025-07-28 23:15:51,750 INFO    MainThread:3195465 [wandb_init.py:init():891] updated telemetry
2025-07-28 23:15:51,937 INFO    MainThread:3195465 [wandb_init.py:init():915] communicating run to backend with 90.0 second timeout
2025-07-28 23:15:54,260 INFO    MainThread:3195465 [wandb_init.py:init():990] starting run threads in backend
2025-07-28 23:15:55,529 INFO    MainThread:3195465 [wandb_run.py:_console_start():2375] atexit reg
2025-07-28 23:15:55,530 INFO    MainThread:3195465 [wandb_run.py:_redirect():2227] redirect: wrap_raw
2025-07-28 23:15:55,530 INFO    MainThread:3195465 [wandb_run.py:_redirect():2292] Wrapping output streams.
2025-07-28 23:15:55,530 INFO    MainThread:3195465 [wandb_run.py:_redirect():2315] Redirects installed.
2025-07-28 23:15:55,533 INFO    MainThread:3195465 [wandb_init.py:init():1032] run started, returning control to user process
2025-07-28 23:16:40,543 INFO    MsgRouterThr:3195465 [mailbox.py:close():129] Closing mailbox, abandoning 2 handles.
2025-07-28 23:16:44,472 ERROR   Thread-4  :3195465 [redirect.py:_on_write():661] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/threading.py", line 932, in _bootstrap_inner
    self.run()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/_utils/pin_memory.py", line 28, in _pin_memory_loop
    r = in_queue.get(timeout=MP_STATUS_CHECK_INTERVAL)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/multiprocessing/queues.py", line 116, in get
    return _ForkingPickler.loads(res)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/multiprocessing/reductions.py", line 297, in rebuild_storage_fd
    fd = df.detach()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/multiprocessing/resource_sharer.py", line 57, in detach
    with _resource_sharer.get_connection(self._id) as conn:
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/multiprocessing/resource_sharer.py", line 87, in get_connection
    c = Client(address, authkey=process.current_process().authkey)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/multiprocessing/connection.py", line 508, in Client
    answer_challenge(c, authkey)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/multiprocessing/connection.py", line 752, in answer_challenge
    message = connection.recv_bytes(256)         # reject large message
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/multiprocessing/connection.py", line 216, in recv_bytes
    buf = self._recv_bytes(maxlength)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/multiprocessing/connection.py", line 414, in _recv_bytes
    buf = self._recv(4)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/multiprocessing/connection.py", line 379, in _recv
    chunk = read(handle, remaining)
ConnectionResetError: [Errno 104] Connection reset by peer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/lib/redirect.py", line 659, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/wandb_run.py", line 2302, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/wandb_run.py", line 401, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/wandb_run.py", line 1444, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/interface/interface.py", line 761, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/lib/sock_client.py", line 174, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/lib/sock_client.py", line 154, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/lib/sock_client.py", line 151, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/lib/sock_client.py", line 130, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
