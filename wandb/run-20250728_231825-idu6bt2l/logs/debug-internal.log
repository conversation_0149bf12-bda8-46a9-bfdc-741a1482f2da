{"time":"2025-07-28T23:18:25.343838556+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250728_231825-idu6bt2l/logs/debug-core.log"}
{"time":"2025-07-28T23:18:25.456758237+08:00","level":"INFO","msg":"created new stream","id":"idu6bt2l"}
{"time":"2025-07-28T23:18:25.456832553+08:00","level":"INFO","msg":"stream: started","id":"idu6bt2l"}
{"time":"2025-07-28T23:18:25.459452808+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"idu6bt2l"}
{"time":"2025-07-28T23:18:25.459442373+08:00","level":"INFO","msg":"handler: started","stream_id":"idu6bt2l"}
{"time":"2025-07-28T23:18:25.459527078+08:00","level":"INFO","msg":"sender: started","stream_id":"idu6bt2l"}
{"time":"2025-07-28T23:18:28.858154925+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-29T00:48:20.299310249+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-728/idu6bt2l/file_stream\": EOF"}
{"time":"2025-07-29T03:30:32.130597038+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-29T09:59:35.244144005+08:00","level":"INFO","msg":"stream: closing","id":"idu6bt2l"}
{"time":"2025-07-29T09:59:35.244178773+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-29T09:59:35.245151138+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-29T09:59:36.981673335+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-29T09:59:38.205630088+08:00","level":"INFO","msg":"handler: closed","stream_id":"idu6bt2l"}
{"time":"2025-07-29T09:59:38.205682222+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"idu6bt2l"}
{"time":"2025-07-29T09:59:38.205746345+08:00","level":"INFO","msg":"sender: closed","stream_id":"idu6bt2l"}
{"time":"2025-07-29T09:59:38.205756266+08:00","level":"INFO","msg":"stream: closed","id":"idu6bt2l"}
