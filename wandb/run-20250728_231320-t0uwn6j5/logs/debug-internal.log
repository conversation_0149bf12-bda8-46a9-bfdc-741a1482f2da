{"time":"2025-07-28T23:13:20.380867371+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250728_231320-t0uwn6j5/logs/debug-core.log"}
{"time":"2025-07-28T23:13:20.601551297+08:00","level":"INFO","msg":"created new stream","id":"t0uwn6j5"}
{"time":"2025-07-28T23:13:20.601619271+08:00","level":"INFO","msg":"stream: started","id":"t0uwn6j5"}
{"time":"2025-07-28T23:13:20.601674499+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"t0uwn6j5"}
{"time":"2025-07-28T23:13:20.60180133+08:00","level":"INFO","msg":"handler: started","stream_id":"t0uwn6j5"}
{"time":"2025-07-28T23:13:20.601821682+08:00","level":"INFO","msg":"sender: started","stream_id":"t0uwn6j5"}
{"time":"2025-07-28T23:13:21.417406051+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-29T01:43:13.434639484+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-728/t0uwn6j5/file_stream\": EOF"}
{"time":"2025-07-29T01:51:25.060894427+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-29T06:02:22.59131104+08:00","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"context deadline exceeded\",\"path\":[\"project\",\"run\"]}],\"data\":{\"project\":{\"run\":null}}}"}
{"time":"2025-07-29T09:59:52.433956782+08:00","level":"INFO","msg":"stream: closing","id":"t0uwn6j5"}
{"time":"2025-07-29T09:59:52.433988717+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-29T09:59:52.449314211+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-29T09:59:53.96177857+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-29T09:59:55.14133706+08:00","level":"INFO","msg":"handler: closed","stream_id":"t0uwn6j5"}
{"time":"2025-07-29T09:59:55.141391005+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"t0uwn6j5"}
{"time":"2025-07-29T09:59:55.141454387+08:00","level":"INFO","msg":"sender: closed","stream_id":"t0uwn6j5"}
{"time":"2025-07-29T09:59:55.141464245+08:00","level":"INFO","msg":"stream: closed","id":"t0uwn6j5"}
