{"time":"2025-07-11T16:07:48.721543842+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250711_160748-0ms4yqqi/logs/debug-core.log"}
{"time":"2025-07-11T16:07:48.841291982+08:00","level":"INFO","msg":"created new stream","id":"0ms4yqqi"}
{"time":"2025-07-11T16:07:48.841346847+08:00","level":"INFO","msg":"stream: started","id":"0ms4yqqi"}
{"time":"2025-07-11T16:07:48.841369036+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"0ms4yqqi"}
{"time":"2025-07-11T16:07:48.84141139+08:00","level":"INFO","msg":"handler: started","stream_id":"0ms4yqqi"}
{"time":"2025-07-11T16:07:48.841420578+08:00","level":"INFO","msg":"sender: started","stream_id":"0ms4yqqi"}
{"time":"2025-07-11T16:07:49.521632148+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-11T19:25:10.570157475+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-11T19:39:40.570381684+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-11T20:19:40.570043021+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-11T22:04:25.57050156+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-11T22:23:55.572406739+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-11T22:35:40.570652605+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T00:00:40.570404316+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T00:03:25.57054692+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T00:03:32.958867279+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T01:20:40.570760278+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T02:11:26.002318647+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-12T04:04:58.908869268+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": local error: tls: bad record MAC"}
{"time":"2025-07-12T04:41:39.631616693+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T04:58:39.631786325+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T06:54:54.630794536+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T07:40:54.631010882+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T07:44:39.631161794+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T08:00:24.630595129+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T11:21:24.631792742+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T11:58:54.631755127+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T15:01:38.960463869+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T16:56:09.63137462+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-709/0ms4yqqi/file_stream\": EOF"}
{"time":"2025-07-12T21:45:03.314806745+08:00","level":"INFO","msg":"stream: closing","id":"0ms4yqqi"}
{"time":"2025-07-12T21:45:03.314866895+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-12T21:45:03.316020131+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-12T21:45:05.122339344+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-12T21:45:05.726096824+08:00","level":"INFO","msg":"handler: closed","stream_id":"0ms4yqqi"}
{"time":"2025-07-12T21:45:05.726158941+08:00","level":"INFO","msg":"sender: closed","stream_id":"0ms4yqqi"}
{"time":"2025-07-12T21:45:05.726141034+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"0ms4yqqi"}
{"time":"2025-07-12T21:45:05.726243593+08:00","level":"INFO","msg":"stream: closed","id":"0ms4yqqi"}
