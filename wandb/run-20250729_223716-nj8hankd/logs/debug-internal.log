{"time":"2025-07-29T22:37:16.754973504+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250729_223716-nj8hankd/logs/debug-core.log"}
{"time":"2025-07-29T22:37:16.864858956+08:00","level":"INFO","msg":"created new stream","id":"nj8hankd"}
{"time":"2025-07-29T22:37:16.864923246+08:00","level":"INFO","msg":"stream: started","id":"nj8hankd"}
{"time":"2025-07-29T22:37:16.864964854+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"nj8hankd"}
{"time":"2025-07-29T22:37:16.864983859+08:00","level":"INFO","msg":"sender: started","stream_id":"nj8hankd"}
{"time":"2025-07-29T22:37:16.865037199+08:00","level":"INFO","msg":"handler: started","stream_id":"nj8hankd"}
{"time":"2025-07-29T22:37:17.55800422+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-29T23:34:08.542406028+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nj8hankd/file_stream\": EOF"}
{"time":"2025-07-30T03:03:08.541763278+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nj8hankd/file_stream\": EOF"}
{"time":"2025-07-30T08:07:53.542094737+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nj8hankd/file_stream\": EOF"}
{"time":"2025-07-30T09:45:23.541308882+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nj8hankd/file_stream\": EOF"}
{"time":"2025-07-30T09:45:31.081458253+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nj8hankd/file_stream\": EOF"}
{"time":"2025-07-30T09:45:40.947478716+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nj8hankd/file_stream\": EOF"}
{"time":"2025-07-30T09:46:15.990303091+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-30T09:46:48.372062357+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-30T09:47:22.830471946+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-30T09:48:01.078816819+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-30T09:48:50.689745473+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-30T11:08:03.833288259+08:00","level":"INFO","msg":"stream: closing","id":"nj8hankd"}
{"time":"2025-07-30T11:08:03.833321365+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-30T11:08:03.834270793+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-30T11:08:05.522157452+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-30T11:08:07.476681697+08:00","level":"INFO","msg":"handler: closed","stream_id":"nj8hankd"}
{"time":"2025-07-30T11:08:07.476752969+08:00","level":"INFO","msg":"sender: closed","stream_id":"nj8hankd"}
{"time":"2025-07-30T11:08:07.476745927+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"nj8hankd"}
{"time":"2025-07-30T11:08:07.476828636+08:00","level":"INFO","msg":"stream: closed","id":"nj8hankd"}
