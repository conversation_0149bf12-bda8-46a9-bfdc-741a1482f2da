特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████| 554/554 [25:39<00:00,  2.78s/it]
处理模板数据: 100%|███████████████████████████████| 151/151 [00:32<00:00,  4.71it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [01:00<00:00, 14.51it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:21<00:00,  4.47it/s]
Epoch-0, seen -- Mean err: 4.66, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 1.58 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [05:19<00:00, 18.96it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:17<00:00,  6.57it/s]
Epoch-0, unseen -- Mean err: 5.68, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 0: 5.72 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [04:44<00:00, 23.36it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.84it/s]
Epoch-0, seen_occ -- Mean err: 11.44, Acc: 0.87, Rec : 0.95, Class and Pose  : 0.86
Validation time for epoch 0: 5.06 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:08<00:00, 24.76it/s]
100%|█████████████████████████████████████████████| 554/554 [25:05<00:00,  2.72s/it]
Epoch-0, unseen_occ -- Mean err: 10.96, Acc: 0.83, Rec : 1.00, Class and Pose  : 0.82
Validation time for epoch 0: 1.28 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:32<00:00,  4.61it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:59<00:00, 14.69it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:20<00:00,  4.56it/s]
Epoch-1, seen -- Mean err: 4.91, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 1.58 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [05:58<00:00, 16.92it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:17<00:00,  6.58it/s]
Epoch-1, unseen -- Mean err: 7.14, Acc: 0.94, Rec : 0.99, Class and Pose  : 0.94
Validation time for epoch 1: 6.37 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [04:42<00:00, 23.51it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.73it/s]
Epoch-1, seen_occ -- Mean err: 14.89, Acc: 0.80, Rec : 0.91, Class and Pose  : 0.80
Validation time for epoch 1: 5.04 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:03<00:00, 26.55it/s]
100%|█████████████████████████████████████████████| 554/554 [24:24<00:00,  2.64s/it]
Epoch-1, unseen_occ -- Mean err: 12.29, Acc: 0.80, Rec : 0.98, Class and Pose  : 0.79
Validation time for epoch 1: 1.21 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:32<00:00,  4.67it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [01:01<00:00, 14.12it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:21<00:00,  4.51it/s]
Epoch-2, seen -- Mean err: 4.70, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 1.61 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [06:30<00:00, 15.51it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:18<00:00,  6.13it/s]
Epoch-2, unseen -- Mean err: 6.42, Acc: 0.96, Rec : 0.99, Class and Pose  : 0.96
Validation time for epoch 2: 6.90 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [04:46<00:00, 23.21it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  6.00it/s]
Epoch-2, seen_occ -- Mean err: 14.48, Acc: 0.82, Rec : 0.91, Class and Pose  : 0.81
Validation time for epoch 2: 5.12 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:04<00:00, 26.36it/s]
100%|█████████████████████████████████████████████| 554/554 [23:30<00:00,  2.55s/it]
Epoch-2, unseen_occ -- Mean err: 11.96, Acc: 0.80, Rec : 0.98, Class and Pose  : 0.79
Validation time for epoch 2: 1.21 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:32<00:00,  4.70it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:59<00:00, 14.63it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:21<00:00,  4.52it/s]
Epoch-3, seen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 1.57 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [06:21<00:00, 15.90it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.64it/s]
Epoch-3, unseen -- Mean err: 6.27, Acc: 0.96, Rec : 1.00, Class and Pose  : 0.96
Validation time for epoch 3: 6.74 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [05:18<00:00, 20.86it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.75it/s]
Epoch-3, seen_occ -- Mean err: 14.18, Acc: 0.82, Rec : 0.91, Class and Pose  : 0.82
Validation time for epoch 3: 5.75 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:05<00:00, 25.58it/s]
100%|█████████████████████████████████████████████| 554/554 [23:02<00:00,  2.50s/it]
Epoch-3, unseen_occ -- Mean err: 11.46, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.81
Validation time for epoch 3: 1.24 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:32<00:00,  4.61it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:59<00:00, 14.54it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:20<00:00,  4.58it/s]
Epoch-4, seen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 1.58 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [06:26<00:00, 15.66it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.67it/s]
Epoch-4, unseen -- Mean err: 6.47, Acc: 0.96, Rec : 0.99, Class and Pose  : 0.96
Validation time for epoch 4: 6.85 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [05:52<00:00, 18.88it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.74it/s]
Epoch-4, seen_occ -- Mean err: 14.79, Acc: 0.81, Rec : 0.91, Class and Pose  : 0.80
Validation time for epoch 4: 6.31 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:04<00:00, 26.34it/s]
100%|█████████████████████████████████████████████| 554/554 [22:35<00:00,  2.45s/it]
Epoch-4, unseen_occ -- Mean err: 11.44, Acc: 0.82, Rec : 0.98, Class and Pose  : 0.81
Validation time for epoch 4: 1.22 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:32<00:00,  4.64it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:59<00:00, 14.57it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:20<00:00,  4.65it/s]
Epoch-5, seen -- Mean err: 4.42, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 5: 1.58 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [06:16<00:00, 16.09it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.63it/s]
Epoch-5, unseen -- Mean err: 6.03, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 5: 6.66 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [06:20<00:00, 17.46it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.65it/s]
Epoch-5, seen_occ -- Mean err: 14.18, Acc: 0.83, Rec : 0.91, Class and Pose  : 0.82
Validation time for epoch 5: 6.79 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:04<00:00, 26.37it/s]
100%|█████████████████████████████████████████████| 554/554 [22:07<00:00,  2.40s/it]
Epoch-5, unseen_occ -- Mean err: 11.02, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 5: 1.22 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:32<00:00,  4.63it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:59<00:00, 14.64it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:20<00:00,  4.61it/s]
Epoch-6, seen -- Mean err: 4.46, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 6: 1.58 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [06:26<00:00, 15.69it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.64it/s]
Epoch-6, unseen -- Mean err: 6.10, Acc: 0.96, Rec : 1.00, Class and Pose  : 0.96
Validation time for epoch 6: 6.82 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [06:48<00:00, 16.28it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:07<00:00,  5.34it/s]
Epoch-6, seen_occ -- Mean err: 14.91, Acc: 0.82, Rec : 0.90, Class and Pose  : 0.81
Validation time for epoch 6: 7.25 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:06<00:00, 25.36it/s]
100%|█████████████████████████████████████████████| 554/554 [21:18<00:00,  2.31s/it]
Epoch-6, unseen_occ -- Mean err: 11.27, Acc: 0.81, Rec : 0.98, Class and Pose  : 0.81
Validation time for epoch 6: 1.26 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:32<00:00,  4.61it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [01:00<00:00, 14.51it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:20<00:00,  4.64it/s]
Epoch-7, seen -- Mean err: 4.34, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 7: 1.60 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [06:24<00:00, 15.75it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.63it/s]
Epoch-7, unseen -- Mean err: 6.01, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 7: 6.79 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [07:04<00:00, 15.67it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.37it/s]
Epoch-7, seen_occ -- Mean err: 13.94, Acc: 0.83, Rec : 0.90, Class and Pose  : 0.82
Validation time for epoch 7: 7.52 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:14<00:00, 22.53it/s]
100%|█████████████████████████████████████████████| 554/554 [20:39<00:00,  2.24s/it]
Epoch-7, unseen_occ -- Mean err: 11.09, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 7: 1.43 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:32<00:00,  4.62it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:58<00:00, 14.81it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:19<00:00,  4.77it/s]
Epoch-8, seen -- Mean err: 4.32, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 8: 1.57 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [06:17<00:00, 16.07it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.55it/s]
Epoch-8, unseen -- Mean err: 6.29, Acc: 0.96, Rec : 1.00, Class and Pose  : 0.96
Validation time for epoch 8: 6.65 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [07:01<00:00, 15.78it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.49it/s]
Epoch-8, seen_occ -- Mean err: 14.22, Acc: 0.83, Rec : 0.90, Class and Pose  : 0.82
Validation time for epoch 8: 7.47 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:36<00:00, 17.49it/s]
100%|█████████████████████████████████████████████| 554/554 [20:22<00:00,  2.21s/it]
Epoch-8, unseen_occ -- Mean err: 11.05, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 8: 1.78 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:32<00:00,  4.67it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:58<00:00, 14.87it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:20<00:00,  4.55it/s]
Epoch-9, seen -- Mean err: 4.36, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 9: 1.55 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [06:19<00:00, 15.99it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.70it/s]
Epoch-9, unseen -- Mean err: 6.31, Acc: 0.96, Rec : 1.00, Class and Pose  : 0.96
Validation time for epoch 9: 6.70 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [07:02<00:00, 15.72it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.30it/s]
Epoch-9, seen_occ -- Mean err: 14.47, Acc: 0.82, Rec : 0.90, Class and Pose  : 0.81
Validation time for epoch 9: 7.48 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:38<00:00, 17.20it/s]
100%|█████████████████████████████████████████████| 554/554 [20:16<00:00,  2.20s/it]
Epoch-9, unseen_occ -- Mean err: 11.55, Acc: 0.81, Rec : 0.99, Class and Pose  : 0.80
Validation time for epoch 9: 1.82 minutes
   📉 学习率已衰减，当前学习率: 2e-05
处理模板数据: 100%|███████████████████████████████| 151/151 [00:32<00:00,  4.70it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:58<00:00, 14.91it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:20<00:00,  4.63it/s]
Epoch-10, seen -- Mean err: 4.33, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 10: 1.55 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [06:18<00:00, 16.03it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.69it/s]
Epoch-10, unseen -- Mean err: 6.30, Acc: 0.96, Rec : 1.00, Class and Pose  : 0.96
Validation time for epoch 10: 6.68 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [07:01<00:00, 15.78it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.44it/s]
Epoch-10, seen_occ -- Mean err: 13.93, Acc: 0.83, Rec : 0.90, Class and Pose  : 0.82
Validation time for epoch 10: 7.46 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:37<00:00, 17.31it/s]
100%|█████████████████████████████████████████████| 554/554 [20:37<00:00,  2.23s/it]
Epoch-10, unseen_occ -- Mean err: 11.23, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 10: 1.80 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:18<00:00,  8.03it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:56<00:00, 15.43it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:20<00:00,  4.60it/s]
Epoch-11, seen -- Mean err: 4.29, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 11: 1.29 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [06:18<00:00, 16.02it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.56it/s]
Epoch-11, unseen -- Mean err: 6.06, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 11: 6.69 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [07:01<00:00, 15.77it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.34it/s]
Epoch-11, seen_occ -- Mean err: 13.76, Acc: 0.83, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 11: 7.47 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:38<00:00, 17.07it/s]
100%|█████████████████████████████████████████████| 554/554 [21:13<00:00,  2.30s/it]
Epoch-11, unseen_occ -- Mean err: 11.15, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 11: 1.83 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:23<00:00,  6.50it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:37<00:00, 23.18it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:14<00:00,  6.35it/s]
Epoch-12, seen -- Mean err: 4.29, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 12: 1.05 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [06:00<00:00, 16.82it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.56it/s]
Epoch-12, unseen -- Mean err: 6.03, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 12: 6.29 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [07:01<00:00, 15.77it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.41it/s]
Epoch-12, seen_occ -- Mean err: 13.77, Acc: 0.83, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 12: 7.48 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:37<00:00, 17.36it/s]
100%|█████████████████████████████████████████████| 554/554 [22:01<00:00,  2.39s/it]
Epoch-12, unseen_occ -- Mean err: 11.17, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 12: 1.80 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:23<00:00,  6.34it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:37<00:00, 23.10it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:15<00:00,  6.10it/s]
Epoch-13, seen -- Mean err: 4.31, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 13: 1.06 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [05:23<00:00, 18.75it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.61it/s]
Epoch-13, unseen -- Mean err: 6.03, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 13: 5.68 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [07:01<00:00, 15.78it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.32it/s]
Epoch-13, seen_occ -- Mean err: 13.62, Acc: 0.83, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 13: 7.46 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:37<00:00, 17.40it/s]
100%|█████████████████████████████████████████████| 554/554 [22:31<00:00,  2.44s/it]
Epoch-13, unseen_occ -- Mean err: 11.14, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 13: 1.80 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:23<00:00,  6.42it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:40<00:00, 21.28it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:15<00:00,  6.31it/s]
Epoch-14, seen -- Mean err: 4.28, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 14: 1.11 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [04:42<00:00, 21.44it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.58it/s]
Epoch-14, unseen -- Mean err: 6.08, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 14: 5.00 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [07:02<00:00, 15.75it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.39it/s]
Epoch-14, seen_occ -- Mean err: 13.41, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 14: 7.48 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:37<00:00, 17.36it/s]
100%|█████████████████████████████████████████████| 554/554 [23:24<00:00,  2.53s/it]
Epoch-14, unseen_occ -- Mean err: 11.11, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 14: 1.80 minutes
   📉 学习率已衰减，当前学习率: 4.000000000000001e-06
处理模板数据: 100%|███████████████████████████████| 151/151 [00:23<00:00,  6.40it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:37<00:00, 23.17it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:15<00:00,  6.26it/s]
Epoch-15, seen -- Mean err: 4.28, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 15: 1.05 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [04:18<00:00, 23.46it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:24<00:00,  4.67it/s]
Epoch-15, unseen -- Mean err: 6.10, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 15: 4.59 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [07:04<00:00, 15.67it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.37it/s]
Epoch-15, seen_occ -- Mean err: 13.50, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 15: 7.52 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:38<00:00, 17.14it/s]
100%|█████████████████████████████████████████████| 554/554 [23:48<00:00,  2.58s/it]
Epoch-15, unseen_occ -- Mean err: 11.09, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 15: 1.82 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:23<00:00,  6.43it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:38<00:00, 22.78it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:14<00:00,  6.42it/s]
Epoch-16, seen -- Mean err: 4.31, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 16: 1.07 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [04:07<00:00, 24.47it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:17<00:00,  6.46it/s]
Epoch-16, unseen -- Mean err: 6.08, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 16: 4.41 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [06:48<00:00, 16.28it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.58it/s]
Epoch-16, seen_occ -- Mean err: 13.46, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 16: 7.13 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:38<00:00, 17.13it/s]
100%|█████████████████████████████████████████████| 554/554 [24:31<00:00,  2.66s/it]
Epoch-16, unseen_occ -- Mean err: 11.01, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 16: 1.81 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:23<00:00,  6.51it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:42<00:00, 20.76it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:15<00:00,  6.33it/s]
Epoch-17, seen -- Mean err: 4.28, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 17: 1.13 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [04:06<00:00, 24.56it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:17<00:00,  6.30it/s]
Epoch-17, unseen -- Mean err: 6.05, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 17: 4.40 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [06:24<00:00, 17.30it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:09<00:00,  4.07it/s]
Epoch-17, seen_occ -- Mean err: 13.41, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 17: 6.74 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:38<00:00, 17.07it/s]
100%|█████████████████████████████████████████████| 554/554 [24:42<00:00,  2.68s/it]
Epoch-17, unseen_occ -- Mean err: 11.06, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 17: 1.85 minutes
处理模板数据: 100%|███████████████████████████████| 151/151 [00:23<00:00,  6.37it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 872/872 [00:37<00:00, 23.38it/s]
处理模板数据: 100%|█████████████████████████████████| 95/95 [00:15<00:00,  6.21it/s]
Epoch-18, seen -- Mean err: 4.29, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 18: 1.05 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6061/6061 [04:09<00:00, 24.32it/s]
处理模板数据: 100%|███████████████████████████████| 113/113 [00:17<00:00,  6.48it/s]
Epoch-18, unseen -- Mean err: 6.00, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 18: 4.44 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 6649/6649 [05:46<00:00, 19.20it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.25it/s]
Epoch-18, seen_occ -- Mean err: 13.36, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 18: 6.10 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 1688/1688 [01:38<00:00, 17.13it/s]
 86%|██████████████████████████████████████▋      | 477/554 [17:45<02:52,  2.23s/it]
Epoch-18, unseen_occ -- Mean err: 11.03, Acc: 0.82, Rec : 0.99, Class and Pose  : 0.82
Validation time for epoch 18: 1.83 minutes
 76%|██████████████████████████████▍         | 19/25 [12:30:44<3:57:04, 2370.76s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
Traceback (most recent call last):
  File "train_new.py", line 443, in <module>
    main()
  File "train_new.py", line 328, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 173, in train
    neg_features = model(neg)  # 返回字典
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 281, in forward
    features = self.aggregation_network(cls_tokens, patch_tokens, mask)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 136, in forward
    cls_feature = F.normalize(cls_feature, p=2, dim=-1)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/functional.py", line 4620, in normalize
    denom = input.norm(p, dim, keepdim=True).clamp_min(eps).expand_as(input)
KeyboardInterrupt
