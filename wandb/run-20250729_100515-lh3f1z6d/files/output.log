特征提取器可训练参数数量: 2.36M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 2.36M
100%|███████████████████████████████████████████| 623/623 [1:01:30<00:00,  5.92s/it]
处理模板数据: 100%|███████████████████████████████| 170/170 [01:13<00:00,  2.31it/s]6s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:07<00:00, 14.44it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-0, seen -- Mean err: 4.93, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 2.41 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:01<00:00, 16.10it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:33<00:00,  2.30it/s]
Epoch-0, unseen -- Mean err: 4.50, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 5.61 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:14<00:00, 16.81it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:16<00:00,  2.27it/s]
Epoch-0, seen_occ -- Mean err: 15.37, Acc: 0.77, Rec : 0.88, Class and Pose  : 0.77
Validation time for epoch 0: 4.83 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:22<00:00, 16.74it/s]
100%|███████████████████████████████████████████| 623/623 [1:00:40<00:00,  5.84s/it]
Epoch-0, unseen_occ -- Mean err: 8.82, Acc: 0.91, Rec : 0.98, Class and Pose  : 0.91
Validation time for epoch 0: 2.68 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [01:13<00:00,  2.30it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:03<00:00, 15.42it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-1, seen -- Mean err: 4.39, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 1: 2.32 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [04:53<00:00, 16.54it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:33<00:00,  2.30it/s]
Epoch-1, unseen -- Mean err: 4.35, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 1: 5.47 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:17<00:00, 16.58it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:16<00:00,  2.25it/s]
Epoch-1, seen_occ -- Mean err: 15.03, Acc: 0.77, Rec : 0.90, Class and Pose  : 0.77
Validation time for epoch 1: 4.88 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:24<00:00, 16.44it/s]
100%|███████████████████████████████████████████| 623/623 [1:00:50<00:00,  5.86s/it]
Epoch-1, unseen_occ -- Mean err: 9.62, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 1: 2.73 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [01:13<00:00,  2.32it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:08<00:00, 14.27it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-2, seen -- Mean err: 4.65, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 2.41 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [04:54<00:00, 16.48it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-2, unseen -- Mean err: 4.53, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 5.49 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:18<00:00, 16.51it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:16<00:00,  2.24it/s]
Epoch-2, seen_occ -- Mean err: 16.58, Acc: 0.75, Rec : 0.86, Class and Pose  : 0.74
Validation time for epoch 2: 4.90 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:21<00:00, 16.83it/s]
 90%|████████████████████████████████████████▋    | 563/623 [55:01<05:51,  5.86s/it]
Epoch-2, unseen_occ -- Mean err: 10.38, Acc: 0.90, Rec : 0.98, Class and Pose  : 0.90
Validation time for epoch 2: 2.67 minutes
 12%|████▉                                    | 3/25 [4:44:48<34:48:33, 5696.05s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
Traceback (most recent call last):
  File "train_new.py", line 437, in <module>
    else:
  File "train_new.py", line 322, in main
    # 简化版：统一应用学习率衰减
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 173, in train
    neg_features = model(neg)  # 返回字典
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 1375, in forward
    features = self.aggregation_network(
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 1739, in forward
    pose_feature = self.final_projection(multi_scale_features)  # [B, descriptor_size, H, W]
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/container.py", line 139, in forward
    input = module(input)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/conv.py", line 457, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/conv.py", line 453, in _conv_forward
    return F.conv2d(input, weight, bias, self.stride,
KeyboardInterrupt
