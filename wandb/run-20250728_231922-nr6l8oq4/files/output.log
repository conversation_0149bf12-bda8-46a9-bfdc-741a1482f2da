特征提取器可训练参数数量: 2.36M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.13M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.13M
初始阶段优化器参数数量: 2.36M
100%|██████████████████████████████████████████| 623/623 [1:11:08<00:00,  6.85s/it]

🔄 Epoch 0: 切换到Hash训练阶段（实验1：简化版本）
   🎯 目标：复现wrap版本的训练方式
   📝 配置：只使用Hash特征损失 + Hash正则化损失
   📊 简化训练参数统计:
      - 主要模型: 2.36M
      - Hash编码器: 0.13M
      - 总计: 2.50M
   📈 统一学习率: 0.0001
   ✅ 优化器已切换到简化Hash训练模式（等价wrap版本）
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:32<00:00,  1.83it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [03:42<00:00,  4.42it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.33it/s]
Epoch-0, seen -- Mean err: 4.82, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 5.29 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [07:59<00:00, 10.10it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-0, unseen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 8.59 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [05:30<00:00, 12.92it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:16<00:00,  2.30it/s]
Epoch-0, seen_occ -- Mean err: 14.94, Acc: 0.79, Rec : 0.89, Class and Pose  : 0.78
Validation time for epoch 0: 6.11 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:30<00:00, 15.81it/s]
100%|██████████████████████████████████████████| 623/623 [1:09:13<00:00,  6.67s/it]
Epoch-0, unseen_occ -- Mean err: 10.03, Acc: 0.90, Rec : 0.98, Class and Pose  : 0.90
Validation time for epoch 0: 2.83 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.33it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:08<00:00, 14.26it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:39<00:00,  1.92it/s]
Epoch-1, seen -- Mean err: 4.66, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 2.40 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [07:58<00:00, 10.14it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:40<00:00,  1.90it/s]
Epoch-1, unseen -- Mean err: 4.57, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 8.69 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [12:02<00:00,  5.91it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:16<00:00,  2.27it/s]
Epoch-1, seen_occ -- Mean err: 15.02, Acc: 0.78, Rec : 0.91, Class and Pose  : 0.78
Validation time for epoch 1: 12.80 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:32<00:00, 15.60it/s]
100%|██████████████████████████████████████████| 623/623 [1:06:36<00:00,  6.42s/it]
Epoch-1, unseen_occ -- Mean err: 9.46, Acc: 0.91, Rec : 0.98, Class and Pose  : 0.91
Validation time for epoch 1: 2.85 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:14<00:00,  2.29it/s]6s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:02<00:00, 15.82it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.32it/s]
Epoch-2, seen -- Mean err: 4.48, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 2.31 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [06:46<00:00, 11.94it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:36<00:00,  2.09it/s]
Epoch-2, unseen -- Mean err: 4.49, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 7.34 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [08:16<00:00,  8.60it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:16<00:00,  2.30it/s]
Epoch-2, seen_occ -- Mean err: 14.94, Acc: 0.78, Rec : 0.92, Class and Pose  : 0.78
Validation time for epoch 2: 8.94 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:30<00:00, 15.78it/s]
100%|██████████████████████████████████████████| 623/623 [1:07:51<00:00,  6.53s/it]
Epoch-2, unseen_occ -- Mean err: 10.18, Acc: 0.90, Rec : 0.98, Class and Pose  : 0.90
Validation time for epoch 2: 2.83 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.31it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:04<00:00, 15.23it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [01:27<00:00,  1.15s/it]
Epoch-3, seen -- Mean err: 4.53, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 2.36 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [08:41<00:00,  9.30it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-3, unseen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 10.27 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [08:54<00:00,  8.00it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:16<00:00,  2.26it/s]
Epoch-3, seen_occ -- Mean err: 15.38, Acc: 0.78, Rec : 0.91, Class and Pose  : 0.77
Validation time for epoch 3: 9.49 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [04:54<00:00,  8.08it/s]
100%|██████████████████████████████████████████| 623/623 [1:04:30<00:00,  6.21s/it]
Epoch-3, unseen_occ -- Mean err: 10.46, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 3: 5.23 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.32it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:06<00:00, 14.75it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.32it/s]
Epoch-4, seen -- Mean err: 4.60, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 2.36 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [11:04<00:00,  7.30it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.30it/s]
Epoch-4, unseen -- Mean err: 4.46, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 11.66 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [10:04<00:00,  7.06it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:18<00:00,  2.08it/s]
Epoch-4, seen_occ -- Mean err: 15.34, Acc: 0.78, Rec : 0.90, Class and Pose  : 0.77
Validation time for epoch 4: 10.69 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [05:14<00:00,  7.55it/s]
100%|██████████████████████████████████████████| 623/623 [1:08:31<00:00,  6.60s/it]
Epoch-4, unseen_occ -- Mean err: 10.08, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 4: 5.70 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:15<00:00,  2.24it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:58<00:00,  8.30it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.32it/s]
Epoch-5, seen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 5: 3.27 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [07:57<00:00, 10.16it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.33it/s]
Epoch-5, unseen -- Mean err: 4.53, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 5: 8.55 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [07:44<00:00,  9.19it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:16<00:00,  2.32it/s]
Epoch-5, seen_occ -- Mean err: 16.19, Acc: 0.75, Rec : 0.88, Class and Pose  : 0.75
Validation time for epoch 5: 8.31 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [03:18<00:00, 11.97it/s]
100%|██████████████████████████████████████████| 623/623 [1:07:22<00:00,  6.49s/it]
Epoch-5, unseen_occ -- Mean err: 10.34, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 5: 3.62 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.32it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:20<00:00, 12.22it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.33it/s]
Epoch-6, seen -- Mean err: 4.79, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 6: 2.59 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像:  34%|█████████▌                  | 1662/4848 [07:30<14:24,  3.69it/s]
 24%|█████████▎                             | 6/25 [10:39:20<33:44:36, 6393.49s/it]
Traceback (most recent call last):
  File "train_new.py", line 437, in <module>
    main()
  File "train_new.py", line 357, in main
    testing_score = testing_utils_dino.test(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 154, in test
    original_query_features = model(query)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 1372, in forward
    sam_cls_tokens, sam_patch_tokens = self.extract_sam_features(x, sam_layer_indices)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 1321, in extract_sam_features
    _ = self.sam_encoder.image_encoder(x_resized)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 254, in forward
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 139, in forward
    x = x + self.mlp(self.norm2(x))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 107, in forward
    x = self.fc2(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/linear.py", line 114, in forward
    return F.linear(input, self.weight, self.bias)
KeyboardInterrupt
