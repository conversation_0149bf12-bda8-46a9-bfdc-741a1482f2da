特征提取器可训练参数数量: 2.36M
偏移量预测器可训练参数数量: 0.96M
✅ 偏移量预测功能已启用
Hash编码器可训练参数数量: 0.13M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.13M
初始阶段优化器参数数量: 3.32M
100%|██████████████████████████████████████████| 623/623 [1:02:24<00:00,  6.01s/it]

🔄 Epoch 0: 切换到Hash训练阶段（实验1：简化版本）
   🎯 目标：复现wrap版本的训练方式
   📝 配置：只使用Hash特征损失 + Hash正则化损失
   📊 简化训练参数统计:
      - 主要模型: 2.36M
      - 偏移预测器: 0.96M
      - Hash编码器: 0.13M
      - 总计: 3.46M
   📈 统一学习率: 0.0001
   ✅ 优化器已切换到简化Hash训练模式（等价wrap版本）
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.32it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:17<00:00, 12.60it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.27it/s]
Epoch-0, seen -- Mean err: 6.00, Acc: 0.96, Rec : 0.99, Class and Pose  : 0.96
Validation time for epoch 0: 2.56 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [05:47<00:00, 13.95it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-0, unseen -- Mean err: 5.64, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 0: 6.41 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [05:04<00:00, 14.04it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:16<00:00,  2.29it/s]
Epoch-0, seen_occ -- Mean err: 18.13, Acc: 0.74, Rec : 0.88, Class and Pose  : 0.73
Validation time for epoch 0: 5.66 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:47<00:00, 14.16it/s]
100%|██████████████████████████████████████████| 623/623 [1:01:39<00:00,  5.94s/it]
Epoch-0, unseen_occ -- Mean err: 13.96, Acc: 0.82, Rec : 0.96, Class and Pose  : 0.82
Validation time for epoch 0: 3.12 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.30it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:16<00:00, 12.86it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-1, seen -- Mean err: 6.73, Acc: 0.95, Rec : 0.98, Class and Pose  : 0.95
Validation time for epoch 1: 2.56 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [05:50<00:00, 13.82it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.28it/s]
Epoch-1, unseen -- Mean err: 6.48, Acc: 0.95, Rec : 0.99, Class and Pose  : 0.95
Validation time for epoch 1: 6.44 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [05:02<00:00, 14.12it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:16<00:00,  2.27it/s]
Epoch-1, seen_occ -- Mean err: 23.71, Acc: 0.64, Rec : 0.82, Class and Pose  : 0.63
Validation time for epoch 1: 5.64 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:42<00:00, 14.62it/s]
100%|██████████████████████████████████████████| 623/623 [1:01:25<00:00,  5.92s/it]
Epoch-1, unseen_occ -- Mean err: 19.00, Acc: 0.75, Rec : 0.95, Class and Pose  : 0.74
Validation time for epoch 1: 3.02 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.32it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:59<00:00, 16.42it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.32it/s]
Epoch-2, seen -- Mean err: 5.30, Acc: 0.98, Rec : 0.99, Class and Pose  : 0.97
Validation time for epoch 2: 2.26 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [06:10<00:00, 13.09it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.28it/s]
Epoch-2, unseen -- Mean err: 5.81, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 2: 6.75 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [05:17<00:00, 13.44it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:17<00:00,  2.22it/s]
Epoch-2, seen_occ -- Mean err: 20.72, Acc: 0.69, Rec : 0.85, Class and Pose  : 0.68
Validation time for epoch 2: 5.92 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:52<00:00, 13.75it/s]
 24%|██████████▍                                 | 148/623 [15:21<49:16,  6.23s/it]
Epoch-2, unseen_occ -- Mean err: 15.19, Acc: 0.82, Rec : 0.96, Class and Pose  : 0.82
Validation time for epoch 2: 3.23 minutes
✅ Hash编码器已设置为训练模式
 12%|████▊                                   | 3/25 [4:14:48<31:08:34, 5096.12s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True
Traceback (most recent call last):
  File "train_new.py", line 443, in <module>
    main()
  File "train_new.py", line 328, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 173, in train
    neg_features = model(neg)  # 返回字典
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 1372, in forward
    sam_cls_tokens, sam_patch_tokens = self.extract_sam_features(x, sam_layer_indices)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 1321, in extract_sam_features
    _ = self.sam_encoder.image_encoder(x_resized)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 254, in forward
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 139, in forward
    x = x + self.mlp(self.norm2(x))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 106, in forward
    x = self.act(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/activation.py", line 681, in forward
    return F.gelu(input, approximate=self.approximate)
KeyboardInterrupt
