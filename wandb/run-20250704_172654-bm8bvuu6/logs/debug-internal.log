{"time":"2025-07-04T17:26:54.539537883+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250704_172654-bm8bvuu6/logs/debug-core.log"}
{"time":"2025-07-04T17:26:54.76859403+08:00","level":"INFO","msg":"created new stream","id":"bm8bvuu6"}
{"time":"2025-07-04T17:26:54.768638626+08:00","level":"INFO","msg":"stream: started","id":"bm8bvuu6"}
{"time":"2025-07-04T17:26:54.768693549+08:00","level":"INFO","msg":"handler: started","stream_id":"bm8bvuu6"}
{"time":"2025-07-04T17:26:54.768701145+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"bm8bvuu6"}
{"time":"2025-07-04T17:26:54.768752072+08:00","level":"INFO","msg":"sender: started","stream_id":"bm8bvuu6"}
{"time":"2025-07-04T17:26:55.271934719+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-04T19:59:06.68185751+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-04T19:59:11.884956821+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-702/bm8bvuu6/file_stream\": EOF"}
{"time":"2025-07-04T19:59:35.167942802+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-702/bm8bvuu6/file_stream\": EOF"}
{"time":"2025-07-04T19:59:39.077497736+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-04T20:00:13.403728446+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-05T00:37:43.057201707+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-702/bm8bvuu6/file_stream\": EOF"}
{"time":"2025-07-05T01:05:29.736015021+08:00","level":"INFO","msg":"stream: closing","id":"bm8bvuu6"}
{"time":"2025-07-05T01:05:29.736065716+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-05T01:05:29.736987865+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-05T01:05:30.921613975+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-05T01:05:31.265516337+08:00","level":"INFO","msg":"handler: closed","stream_id":"bm8bvuu6"}
{"time":"2025-07-05T01:05:31.265574834+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"bm8bvuu6"}
{"time":"2025-07-05T01:05:31.265659778+08:00","level":"INFO","msg":"sender: closed","stream_id":"bm8bvuu6"}
{"time":"2025-07-05T01:05:31.265678605+08:00","level":"INFO","msg":"stream: closed","id":"bm8bvuu6"}
