{"time":"2025-07-07T10:49:01.850893555+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250707_104901-l7326vb2/logs/debug-core.log"}
{"time":"2025-07-07T10:49:01.963349565+08:00","level":"INFO","msg":"created new stream","id":"l7326vb2"}
{"time":"2025-07-07T10:49:01.963407072+08:00","level":"INFO","msg":"stream: started","id":"l7326vb2"}
{"time":"2025-07-07T10:49:01.963475858+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"l7326vb2"}
{"time":"2025-07-07T10:49:01.963495985+08:00","level":"INFO","msg":"sender: started","stream_id":"l7326vb2"}
{"time":"2025-07-07T10:49:01.96355059+08:00","level":"INFO","msg":"handler: started","stream_id":"l7326vb2"}
{"time":"2025-07-07T10:49:02.61335891+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-07T11:45:33.974230663+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": unexpected EOF"}
{"time":"2025-07-07T12:01:53.5685939+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:14:04.757091122+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:14:07.374515058+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:14:08.659547605+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-07-07T14:14:10.973771452+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-07-07T14:14:11.856237775+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:14:15.67323993+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-07-07T14:14:20.210129393+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:14:37.647267254+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:14:54.184973425+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-07T14:15:13.850624048+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-07-07T14:15:15.090753333+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:16:15.371382011+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:16:22.824492436+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-07T14:17:23.094826521+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-07-07T14:17:45.639765275+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:18:23.369728699+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-07-07T14:18:45.928918113+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:50:37.721992246+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:50:52.723987636+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T14:53:23.741163292+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-07-07T14:53:31.246660132+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-07-07T14:53:37.722258049+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T15:10:07.724117234+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T15:11:07.72364138+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
{"time":"2025-07-07T15:16:07.722050511+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/l7326vb2/file_stream\": EOF"}
