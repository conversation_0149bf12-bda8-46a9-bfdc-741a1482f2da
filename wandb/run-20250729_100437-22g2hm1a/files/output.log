特征提取器可训练参数数量: 3.21M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 3.21M
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [1:00:56<00:00,  5.87s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [01:13<00:00,  2.31it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:06<00:00, 14.80it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:33<00:00,  2.30it/s]
Epoch-0, seen -- Mean err: 4.52, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 2.37 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [04:58<00:00, 16.26it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-0, unseen -- Mean err: 4.16, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 5.56 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:08<00:00, 17.17it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:16<00:00,  2.32it/s]
Epoch-0, seen_occ -- Mean err: 14.20, Acc: 0.81, Rec : 0.93, Class and Pose  : 0.80
Validation time for epoch 0: 4.73 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:13<00:00, 17.81it/s]
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [1:00:19<00:00,  5.81s/it]
Epoch-0, unseen_occ -- Mean err: 8.45, Acc: 0.91, Rec : 0.98, Class and Pose  : 0.91
Validation time for epoch 0: 2.53 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [01:13<00:00,  2.31it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:02<00:00, 15.64it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-1, seen -- Mean err: 4.43, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 2.31 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [04:44<00:00, 17.02it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:33<00:00,  2.30it/s]
Epoch-1, unseen -- Mean err: 4.55, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 5.34 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:12<00:00, 16.94it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:17<00:00,  2.23it/s]
Epoch-1, seen_occ -- Mean err: 14.71, Acc: 0.79, Rec : 0.92, Class and Pose  : 0.79
Validation time for epoch 1: 4.79 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:17<00:00, 17.23it/s]
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [1:00:14<00:00,  5.80s/it]
Epoch-1, unseen_occ -- Mean err: 10.33, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 1: 2.62 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [01:13<00:00,  2.32it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:01<00:00, 15.98it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:33<00:00,  2.30it/s]
Epoch-2, seen -- Mean err: 4.28, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 2.29 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [04:48<00:00, 16.81it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-2, unseen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 5.39 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:12<00:00, 16.89it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:16<00:00,  2.24it/s]
Epoch-2, seen_occ -- Mean err: 15.53, Acc: 0.78, Rec : 0.90, Class and Pose  : 0.77
Validation time for epoch 2: 4.81 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:15<00:00, 17.52it/s]
 97%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▎   | 605/623 [58:24<01:44,  5.79s/it]
Epoch-2, unseen_occ -- Mean err: 10.32, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 2: 2.58 minutes
 12%|███████████████                                                                                                              | 3/25 [4:45:33<34:54:03, 5711.08s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
Traceback (most recent call last):
  File "train_new.py", line 437, in <module>
    else:
  File "train_new.py", line 322, in main
    # 简化版：统一应用学习率衰减
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 89, in train
    neg_batch = torch.stack([pose_negatives[batch_idx][neg_idx].cuda() for batch_idx in range(B)], dim=0)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 89, in <listcomp>
    neg_batch = torch.stack([pose_negatives[batch_idx][neg_idx].cuda() for batch_idx in range(B)], dim=0)
KeyboardInterrupt
