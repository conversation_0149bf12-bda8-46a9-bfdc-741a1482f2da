特征提取器可训练参数数量: 2.36M
偏移量预测器可训练参数数量: 0.96M
✅ 偏移量预测功能已启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 3.32M
100%|███████████████████████████████████████████| 623/623 [1:16:36<00:00,  7.38s/it]
处理模板数据: 100%|███████████████████████████████| 170/170 [01:13<00:00,  2.32it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:14<00:00, 13.17it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:32<00:00,  2.30it/s]
Epoch-0, seen -- Mean err: 6.33, Acc: 0.96, Rec : 0.98, Class and Pose  : 0.96
Validation time for epoch 0: 2.51 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:37<00:00, 14.37it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-0, unseen -- Mean err: 5.53, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 0: 6.20 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:41<00:00, 15.19it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:16<00:00,  2.27it/s]
Epoch-0, seen_occ -- Mean err: 18.34, Acc: 0.73, Rec : 0.89, Class and Pose  : 0.72
Validation time for epoch 0: 5.27 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:33<00:00, 15.45it/s]
100%|███████████████████████████████████████████| 623/623 [1:00:58<00:00,  5.87s/it]
Epoch-0, unseen_occ -- Mean err: 13.02, Acc: 0.82, Rec : 0.97, Class and Pose  : 0.82
Validation time for epoch 0: 2.88 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [01:13<00:00,  2.31it/s]3s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:10<00:00, 13.87it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-1, seen -- Mean err: 5.52, Acc: 0.98, Rec : 0.99, Class and Pose  : 0.98
Validation time for epoch 1: 2.47 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:29<00:00, 14.70it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:33<00:00,  2.27it/s]
Epoch-1, unseen -- Mean err: 5.74, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 6.09 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:56<00:00, 14.40it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:17<00:00,  2.23it/s]
Epoch-1, seen_occ -- Mean err: 19.20, Acc: 0.72, Rec : 0.86, Class and Pose  : 0.71
Validation time for epoch 1: 5.54 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:36<00:00, 15.18it/s]
100%|███████████████████████████████████████████| 623/623 [1:00:46<00:00,  5.85s/it]
Epoch-1, unseen_occ -- Mean err: 14.42, Acc: 0.82, Rec : 0.96, Class and Pose  : 0.82
Validation time for epoch 1: 2.94 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [01:14<00:00,  2.28it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:15<00:00, 12.93it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:33<00:00,  2.26it/s]
Epoch-2, seen -- Mean err: 5.50, Acc: 0.97, Rec : 0.99, Class and Pose  : 0.97
Validation time for epoch 2: 2.57 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [06:26<00:00, 12.55it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-2, unseen -- Mean err: 6.66, Acc: 0.96, Rec : 1.00, Class and Pose  : 0.96
Validation time for epoch 2: 7.04 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:45<00:00, 12.36it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:17<00:00,  2.21it/s]
Epoch-2, seen_occ -- Mean err: 21.35, Acc: 0.69, Rec : 0.85, Class and Pose  : 0.68
Validation time for epoch 2: 6.37 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [03:03<00:00, 12.94it/s]
  1%|▌                                            | 8/623 [01:03<1:20:45,  7.88s/it]
Epoch-2, unseen_occ -- Mean err: 14.14, Acc: 0.83, Rec : 0.96, Class and Pose  : 0.83
Validation time for epoch 2: 3.40 minutes
 12%|████▉                                    | 3/25 [4:13:03<30:55:44, 5061.10s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
Traceback (most recent call last):
  File "train_new.py", line 443, in <module>
    main()
  File "train_new.py", line 328, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 173, in train
    neg_features = model(neg)  # 返回字典
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 1346, in forward
    cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/new_dino_network.py", line 57, in forward
    _ = self.model(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 325, in forward
    ret = self.forward_features(*args, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 261, in forward_features
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 254, in forward
    return super().forward(x_or_x_list)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 113, in forward
    x = x + ffn_residual_func(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 94, in ffn_residual_func
    return self.ls2(self.mlp(self.norm2(x)))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/mlp.py", line 38, in forward
    x = self.fc2(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/linear.py", line 114, in forward
    return F.linear(input, self.weight, self.bias)
KeyboardInterrupt
