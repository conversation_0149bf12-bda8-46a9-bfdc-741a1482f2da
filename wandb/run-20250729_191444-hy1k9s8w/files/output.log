特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████| 621/621 [18:36<00:00,  1.80s/it]
处理模板数据: 100%|███████████████████████████████| 170/170 [00:20<00:00,  8.31it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [00:56<00:00, 17.35it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:09<00:00,  7.92it/s]
Epoch-0, seen -- Mean err: 4.87, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 1.33 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4874/4874 [04:09<00:00, 19.55it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:09<00:00,  8.16it/s]
Epoch-0, unseen -- Mean err: 4.68, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 4.37 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:03<00:00, 19.27it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:10<00:00,  7.41it/s]
Epoch-0, seen_occ -- Mean err: 10.41, Acc: 0.86, Rec : 0.95, Class and Pose  : 0.86
Validation time for epoch 0: 2.26 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [03:28<00:00, 20.50it/s]
100%|█████████████████████████████████████████████| 621/621 [17:15<00:00,  1.67s/it]
Epoch-0, unseen_occ -- Mean err: 15.55, Acc: 0.79, Rec : 0.92, Class and Pose  : 0.79
Validation time for epoch 0: 3.70 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:18<00:00,  9.15it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [00:35<00:00, 27.38it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.63it/s]
Epoch-1, seen -- Mean err: 5.05, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 0.94 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4874/4874 [02:48<00:00, 28.90it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.69it/s]
Epoch-1, unseen -- Mean err: 5.11, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 2.99 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [01:33<00:00, 25.39it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.62it/s]
Epoch-1, seen_occ -- Mean err: 12.70, Acc: 0.83, Rec : 0.93, Class and Pose  : 0.82
Validation time for epoch 1: 1.74 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [02:39<00:00, 26.84it/s]
100%|█████████████████████████████████████████████| 621/621 [16:34<00:00,  1.60s/it]
Epoch-1, unseen_occ -- Mean err: 17.86, Acc: 0.74, Rec : 0.88, Class and Pose  : 0.73
Validation time for epoch 1: 2.84 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:18<00:00,  9.05it/s]5s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [00:35<00:00, 27.31it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.83it/s]
Epoch-2, seen -- Mean err: 4.95, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 0.95 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4874/4874 [02:34<00:00, 31.55it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.97it/s]
Epoch-2, unseen -- Mean err: 5.23, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 2: 2.75 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [01:17<00:00, 30.86it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.98it/s]
Epoch-2, seen_occ -- Mean err: 12.62, Acc: 0.83, Rec : 0.93, Class and Pose  : 0.83
Validation time for epoch 2: 1.46 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [02:54<00:00, 24.46it/s]
100%|█████████████████████████████████████████████| 621/621 [16:42<00:00,  1.61s/it]
Epoch-2, unseen_occ -- Mean err: 18.07, Acc: 0.74, Rec : 0.88, Class and Pose  : 0.74
Validation time for epoch 2: 3.09 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:19<00:00,  8.73it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [00:44<00:00, 22.04it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.47it/s]
Epoch-3, seen -- Mean err: 4.94, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 1.11 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4874/4874 [03:07<00:00, 25.93it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:09<00:00,  8.44it/s]
Epoch-3, unseen -- Mean err: 5.17, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 3: 3.32 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [01:38<00:00, 24.22it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.76it/s]
Epoch-3, seen_occ -- Mean err: 13.35, Acc: 0.83, Rec : 0.91, Class and Pose  : 0.82
Validation time for epoch 3: 1.83 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [03:18<00:00, 21.53it/s]
100%|█████████████████████████████████████████████| 621/621 [16:59<00:00,  1.64s/it]
Epoch-3, unseen_occ -- Mean err: 19.85, Acc: 0.70, Rec : 0.86, Class and Pose  : 0.70
Validation time for epoch 3: 3.50 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:19<00:00,  8.83it/s]6s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [00:43<00:00, 22.72it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.70it/s]
Epoch-4, seen -- Mean err: 4.68, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 1.08 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4874/4874 [03:25<00:00, 23.72it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:09<00:00,  8.13it/s]
Epoch-4, unseen -- Mean err: 5.20, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 4: 3.60 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [01:51<00:00, 21.37it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.49it/s]
Epoch-4, seen_occ -- Mean err: 12.19, Acc: 0.84, Rec : 0.93, Class and Pose  : 0.84
Validation time for epoch 4: 2.06 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [02:58<00:00, 23.92it/s]
100%|█████████████████████████████████████████████| 621/621 [17:22<00:00,  1.68s/it]
Epoch-4, unseen_occ -- Mean err: 18.01, Acc: 0.74, Rec : 0.88, Class and Pose  : 0.73
Validation time for epoch 4: 3.16 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:19<00:00,  8.57it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [00:48<00:00, 20.39it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.50it/s]
Epoch-5, seen -- Mean err: 4.55, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 5: 1.18 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4874/4874 [03:17<00:00, 24.64it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.82it/s]
Epoch-5, unseen -- Mean err: 5.04, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 5: 3.50 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [01:28<00:00, 26.83it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:08<00:00,  8.62it/s]
Epoch-5, seen_occ -- Mean err: 12.09, Acc: 0.84, Rec : 0.93, Class and Pose  : 0.84
Validation time for epoch 5: 1.66 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [03:41<00:00, 19.25it/s]
 24%|██████████                                | 6/25 [2:42:27<8:33:32, 1621.74s/it]
Epoch-5, unseen_occ -- Mean err: 17.65, Acc: 0.75, Rec : 0.88, Class and Pose  : 0.74
Validation time for epoch 5: 3.88 minutes
 75%|█████████████████████████████████▌           | 464/621 [12:39<04:08,  1.59s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
