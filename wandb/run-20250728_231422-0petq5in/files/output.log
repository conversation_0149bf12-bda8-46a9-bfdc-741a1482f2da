特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.13M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.13M
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [26:00<00:00,  2.50s/it]

🔄 Epoch 0: 切换到Hash训练阶段（实验1：简化版本）
   🎯 目标：复现wrap版本的训练方式
   📝 配置：只使用Hash特征损失 + Hash正则化损失
   📊 简化训练参数统计:
      - 主要模型: 1.43M
      - Hash编码器: 0.13M
      - 总计: 1.56M
   📈 统一学习率: 0.0001
   ✅ 优化器已切换到简化Hash训练模式（等价wrap版本）
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.70it/s]████████████████████████████████████████████████████████| 623/623 [25:59<00:00,  2.30s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:08<00:00, 14.25it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.62it/s]
Epoch-0, seen -- Mean err: 4.54, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 1.80 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [04:50<00:00, 16.69it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.53it/s]
Epoch-0, unseen -- Mean err: 4.31, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 5.15 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:02<00:00, 17.59it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.62it/s]
Epoch-0, seen_occ -- Mean err: 13.50, Acc: 0.80, Rec : 0.92, Class and Pose  : 0.80
Validation time for epoch 0: 4.36 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:24<00:00, 16.45it/s]
100%|█████████████████████████████████████████████| 623/623 [28:50<00:00,  2.78s/it]                                                | 1/25 [40:00<16:00:12, 2400.53s/it]
Epoch-0, unseen_occ -- Mean err: 8.97, Acc: 0.91, Rec : 0.98, Class and Pose  : 0.91
Validation time for epoch 0: 2.57 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.62it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:08<00:00, 14.39it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.55it/s]
Epoch-1, seen -- Mean err: 4.50, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 1.79 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [04:59<00:00, 16.18it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.59it/s]
Epoch-1, unseen -- Mean err: 4.50, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 5.31 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:30<00:00, 15.81it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:05<00:00,  6.52it/s]
Epoch-1, seen_occ -- Mean err: 14.43, Acc: 0.79, Rec : 0.92, Class and Pose  : 0.79
Validation time for epoch 1: 4.82 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:05<00:00, 19.01it/s]
100%|█████████████████████████████████████████████| 623/623 [30:19<00:00,  2.92s/it]                                              | 2/25 [1:23:07<16:02:15, 2510.22s/it]
Epoch-1, unseen_occ -- Mean err: 10.76, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 1: 2.22 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.67it/s]5s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:18<00:00, 12.51it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.55it/s]
Epoch-2, seen -- Mean err: 4.44, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 1.96 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:20<00:00, 15.12it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.08it/s]
Epoch-2, unseen -- Mean err: 4.66, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 5.67 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:09<00:00, 17.11it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:07<00:00,  4.77it/s]
Epoch-2, seen_occ -- Mean err: 14.72, Acc: 0.79, Rec : 0.89, Class and Pose  : 0.78
Validation time for epoch 2: 4.41 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [03:59<00:00,  9.92it/s]
100%|█████████████████████████████████████████████| 623/623 [29:59<00:00,  2.89s/it]                                              | 3/25 [2:09:46<16:08:47, 2642.16s/it]
Epoch-2, unseen_occ -- Mean err: 11.10, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 2: 4.17 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:29<00:00,  5.73it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [00:49<00:00, 19.98it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.09it/s]
Epoch-3, seen -- Mean err: 4.44, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 1.34 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [03:30<00:00, 23.08it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.62it/s]
Epoch-3, unseen -- Mean err: 4.66, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 3.74 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:20<00:00, 16.40it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.28it/s]
Epoch-3, seen_occ -- Mean err: 14.39, Acc: 0.79, Rec : 0.90, Class and Pose  : 0.79
Validation time for epoch 3: 4.65 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:32<00:00, 15.54it/s]
100%|█████████████████████████████████████████████| 623/623 [29:14<00:00,  2.82s/it]                                              | 4/25 [2:52:20<15:12:34, 2607.36s/it]
Epoch-3, unseen_occ -- Mean err: 10.40, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 3: 2.73 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:27<00:00,  6.27it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [00:48<00:00, 20.37it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.20it/s]
Epoch-4, seen -- Mean err: 4.37, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 4: 1.29 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [04:16<00:00, 18.89it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.58it/s]
Epoch-4, unseen -- Mean err: 4.61, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 4.51 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:23<00:00, 16.23it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.52it/s]
Epoch-4, seen_occ -- Mean err: 16.48, Acc: 0.76, Rec : 0.86, Class and Pose  : 0.75
Validation time for epoch 4: 4.70 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:18<00:00, 17.18it/s]
100%|█████████████████████████████████████████████| 623/623 [28:10<00:00,  2.71s/it]                                              | 5/25 [3:34:39<14:20:54, 2582.74s/it]
Epoch-4, unseen_occ -- Mean err: 11.02, Acc: 0.90, Rec : 0.98, Class and Pose  : 0.90
Validation time for epoch 4: 2.49 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:27<00:00,  6.28it/s]3s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [00:44<00:00, 22.06it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.27it/s]
Epoch-5, seen -- Mean err: 4.38, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 5: 1.27 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [04:53<00:00, 16.52it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.55it/s]
Epoch-5, unseen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 5: 5.13 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:35<00:00, 15.52it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.28it/s]
Epoch-5, seen_occ -- Mean err: 16.45, Acc: 0.75, Rec : 0.86, Class and Pose  : 0.75
Validation time for epoch 5: 4.90 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:13<00:00, 17.80it/s]
100%|█████████████████████████████████████████████| 623/623 [27:23<00:00,  2.64s/it]                                              | 6/25 [4:16:40<13:31:08, 2561.52s/it]
Epoch-5, unseen_occ -- Mean err: 10.37, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 5: 2.43 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:22<00:00,  7.51it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:29<00:00, 10.95it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.64it/s]
Epoch-6, seen -- Mean err: 4.40, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 6: 1.91 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:08<00:00, 15.72it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.67it/s]
Epoch-6, unseen -- Mean err: 5.15, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 6: 5.47 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:54<00:00, 14.53it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:09<00:00,  3.81it/s]
Epoch-6, seen_occ -- Mean err: 14.93, Acc: 0.77, Rec : 0.89, Class and Pose  : 0.77
Validation time for epoch 6: 5.23 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:45<00:00, 14.34it/s]
100%|█████████████████████████████████████████████| 623/623 [26:44<00:00,  2.58s/it]                                              | 7/25 [4:59:48<12:51:06, 2570.34s/it]
Epoch-6, unseen_occ -- Mean err: 11.48, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 6: 3.02 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.60it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:06<00:00, 14.80it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.51it/s]
Epoch-7, seen -- Mean err: 4.44, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 7: 1.75 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [04:51<00:00, 16.65it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:17<00:00,  4.46it/s]
Epoch-7, unseen -- Mean err: 5.07, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 7: 5.19 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:32<00:00, 12.86it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.37it/s]
Epoch-7, seen_occ -- Mean err: 16.78, Acc: 0.75, Rec : 0.86, Class and Pose  : 0.75
Validation time for epoch 7: 5.85 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:38<00:00, 15.00it/s]
100%|█████████████████████████████████████████████| 623/623 [27:21<00:00,  2.64s/it]                                              | 8/25 [5:42:22<12:06:44, 2564.97s/it]
Epoch-7, unseen_occ -- Mean err: 11.74, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 7: 2.86 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.60it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:11<00:00, 13.81it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.49it/s]
Epoch-8, seen -- Mean err: 4.45, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 8: 1.85 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:44<00:00, 14.09it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:17<00:00,  4.47it/s]
Epoch-8, unseen -- Mean err: 4.78, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 8: 6.06 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:34<00:00, 15.58it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.53it/s]
Epoch-8, seen_occ -- Mean err: 15.89, Acc: 0.77, Rec : 0.89, Class and Pose  : 0.76
Validation time for epoch 8: 4.89 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:24<00:00, 16.43it/s]
100%|█████████████████████████████████████████████| 623/623 [26:17<00:00,  2.53s/it]                                              | 9/25 [6:25:14<11:24:35, 2567.22s/it]
Epoch-8, unseen_occ -- Mean err: 11.98, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 8: 2.59 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.67it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:05<00:00, 14.88it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.52it/s]
Epoch-9, seen -- Mean err: 4.36, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 9: 1.75 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [04:56<00:00, 16.34it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.63it/s]
Epoch-9, unseen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 9: 5.25 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:07<00:00, 17.24it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.43it/s]
Epoch-9, seen_occ -- Mean err: 16.09, Acc: 0.77, Rec : 0.86, Class and Pose  : 0.76
Validation time for epoch 9: 4.44 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:13<00:00, 17.75it/s]
100%|█████████████████████████████████████████████| 623/623 [27:07<00:00,  2.61s/it]                                             | 10/25 [7:05:27<10:29:56, 2519.75s/it]
Epoch-9, unseen_occ -- Mean err: 11.58, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 9: 2.41 minutes
   📉 学习率已衰减，当前学习率: 2e-05
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.62it/s]6s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:10<00:00, 13.95it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.64it/s]
Epoch-10, seen -- Mean err: 4.35, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 10: 1.84 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:52<00:00, 13.75it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.62it/s]
Epoch-10, unseen -- Mean err: 4.70, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 10: 6.19 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:25<00:00, 13.13it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.43it/s]
Epoch-10, seen_occ -- Mean err: 16.09, Acc: 0.76, Rec : 0.86, Class and Pose  : 0.76
Validation time for epoch 10: 5.77 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:31<00:00, 15.68it/s]
100%|█████████████████████████████████████████████| 623/623 [27:47<00:00,  2.68s/it]                                              | 11/25 [7:49:14<9:55:33, 2552.41s/it]
Epoch-10, unseen_occ -- Mean err: 12.22, Acc: 0.85, Rec : 0.98, Class and Pose  : 0.84
Validation time for epoch 10: 2.71 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.69it/s]6s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:05<00:00, 15.01it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.67it/s]
Epoch-11, seen -- Mean err: 4.29, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 11: 1.73 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:24<00:00, 14.94it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.55it/s]
Epoch-11, unseen -- Mean err: 4.66, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 11: 5.72 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:00<00:00, 14.23it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.58it/s]
Epoch-11, seen_occ -- Mean err: 16.07, Acc: 0.76, Rec : 0.87, Class and Pose  : 0.76
Validation time for epoch 11: 5.32 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [01:42<00:00, 23.19it/s]
100%|█████████████████████████████████████████████| 623/623 [27:39<00:00,  2.66s/it]                                              | 12/25 [8:31:45<9:12:55, 2551.97s/it]
Epoch-11, unseen_occ -- Mean err: 11.86, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 11: 1.86 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:34<00:00,  4.86it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:34<00:00, 10.40it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.50it/s]
Epoch-12, seen -- Mean err: 4.26, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 12: 2.23 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:36<00:00, 14.41it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.64it/s]
Epoch-12, unseen -- Mean err: 4.71, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 12: 5.95 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:05<00:00, 17.38it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.72it/s]
Epoch-12, seen_occ -- Mean err: 15.66, Acc: 0.77, Rec : 0.88, Class and Pose  : 0.76
Validation time for epoch 12: 4.41 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [01:49<00:00, 21.71it/s]
100%|█████████████████████████████████████████████| 623/623 [29:37<00:00,  2.85s/it]                                              | 13/25 [9:14:05<8:29:42, 2548.57s/it]
Epoch-12, unseen_occ -- Mean err: 11.59, Acc: 0.85, Rec : 0.98, Class and Pose  : 0.85
Validation time for epoch 12: 1.97 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:37<00:00,  4.57it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:06<00:00, 14.81it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:16<00:00,  4.52it/s]
Epoch-13, seen -- Mean err: 4.39, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 13: 1.76 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [04:40<00:00, 17.28it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:22<00:00,  3.41it/s]
Epoch-13, unseen -- Mean err: 4.59, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 13: 4.99 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:22<00:00, 16.30it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:07<00:00,  5.39it/s]
Epoch-13, seen_occ -- Mean err: 18.57, Acc: 0.72, Rec : 0.82, Class and Pose  : 0.72
Validation time for epoch 13: 4.81 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:07<00:00, 18.59it/s]
100%|█████████████████████████████████████████████| 623/623 [30:14<00:00,  2.91s/it]                                              | 14/25 [9:57:44<7:51:06, 2569.66s/it]
Epoch-13, unseen_occ -- Mean err: 11.77, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 13: 2.31 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.65it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:02<00:00, 15.74it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.02it/s]
Epoch-14, seen -- Mean err: 4.25, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 14: 1.69 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [03:22<00:00, 24.00it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.14it/s]
Epoch-14, unseen -- Mean err: 4.66, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 14: 3.60 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [03:07<00:00, 22.73it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.73it/s]
Epoch-14, seen_occ -- Mean err: 16.76, Acc: 0.75, Rec : 0.86, Class and Pose  : 0.75
Validation time for epoch 14: 3.37 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [03:43<00:00, 10.65it/s]
 12%|█████▍                                        | 74/623 [04:06<30:25,  3.33s/it]                                             | 15/25 [10:40:53<7:09:14, 2575.48s/it]
Epoch-14, unseen_occ -- Mean err: 11.61, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 14: 3.87 minutes
   📉 学习率已衰减，当前学习率: 4.000000000000001e-06
✅ Hash编码器已设置为训练模式
 60%|██████████████████████████████████████████████████████████████████████████▍                                                 | 15/25 [10:45:01<7:10:01, 2580.11s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True
Traceback (most recent call last):
  File "train_new.py", line 437, in <module>
    main()
  File "train_new.py", line 322, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 218, in train
    cls_negative_features = model(cls_negative)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 258, in forward
    cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/new_dino_network.py", line 57, in forward
    _ = self.model(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 325, in forward
    ret = self.forward_features(*args, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 261, in forward_features
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 254, in forward
    return super().forward(x_or_x_list)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 113, in forward
    x = x + ffn_residual_func(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 94, in ffn_residual_func
    return self.ls2(self.mlp(self.norm2(x)))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/mlp.py", line 36, in forward
    x = self.act(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1125, in _call_impl
    forward_call = (self._slow_forward if torch._C._get_tracing_state() else self.forward)
KeyboardInterrupt
