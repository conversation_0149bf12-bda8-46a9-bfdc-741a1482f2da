{"time":"2025-07-29T22:39:20.714924227+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250729_223920-p4ga6h1j/logs/debug-core.log"}
{"time":"2025-07-29T22:39:20.83278379+08:00","level":"INFO","msg":"created new stream","id":"p4ga6h1j"}
{"time":"2025-07-29T22:39:20.832847726+08:00","level":"INFO","msg":"stream: started","id":"p4ga6h1j"}
{"time":"2025-07-29T22:39:20.832871127+08:00","level":"INFO","msg":"handler: started","stream_id":"p4ga6h1j"}
{"time":"2025-07-29T22:39:20.832942469+08:00","level":"INFO","msg":"sender: started","stream_id":"p4ga6h1j"}
{"time":"2025-07-29T22:39:20.832966558+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"p4ga6h1j"}
{"time":"2025-07-29T22:39:21.513708539+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-29T23:15:52.763933285+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-29T23:34:30.611817035+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/p4ga6h1j/file_stream\": EOF"}
{"time":"2025-07-29T23:34:37.884802812+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/p4ga6h1j/file_stream\": EOF"}
{"time":"2025-07-29T23:50:45.612691239+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/p4ga6h1j/file_stream\": EOF"}
{"time":"2025-07-30T00:01:00.611163609+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/p4ga6h1j/file_stream\": EOF"}
{"time":"2025-07-30T01:37:30.61114436+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/p4ga6h1j/file_stream\": EOF"}
{"time":"2025-07-30T05:29:30.614521532+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/p4ga6h1j/file_stream\": EOF"}
{"time":"2025-07-30T09:45:15.618875932+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/p4ga6h1j/file_stream\": EOF"}
{"time":"2025-07-30T09:45:22.868725987+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/p4ga6h1j/file_stream\": EOF"}
{"time":"2025-07-30T09:45:32.56465993+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/p4ga6h1j/file_stream\": EOF"}
{"time":"2025-07-30T09:45:47.558441495+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/p4ga6h1j/file_stream\": EOF"}
{"time":"2025-07-30T09:45:55.209087799+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-30T11:08:11.638373423+08:00","level":"INFO","msg":"stream: closing","id":"p4ga6h1j"}
{"time":"2025-07-30T11:08:11.638414358+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-30T11:08:11.63911146+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-30T11:08:13.109456043+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-30T11:08:14.296531168+08:00","level":"INFO","msg":"handler: closed","stream_id":"p4ga6h1j"}
{"time":"2025-07-30T11:08:14.296606433+08:00","level":"INFO","msg":"sender: closed","stream_id":"p4ga6h1j"}
{"time":"2025-07-30T11:08:14.296597834+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"p4ga6h1j"}
{"time":"2025-07-30T11:08:14.296720614+08:00","level":"INFO","msg":"stream: closed","id":"p4ga6h1j"}
