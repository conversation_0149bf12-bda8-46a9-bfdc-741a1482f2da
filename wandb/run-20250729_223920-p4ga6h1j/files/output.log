特征提取器可训练参数数量: 2.36M
偏移量预测器可训练参数数量: 0.96M
✅ 偏移量预测功能已启用
Hash编码器可训练参数数量: 0.13M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.13M
初始阶段优化器参数数量: 3.32M
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 621/621 [1:02:02<00:00,  5.99s/it]

🔄 Epoch 0: 切换到Hash训练阶段（实验1：简化版本）
   🎯 目标：复现wrap版本的训练方式
   📝 配置：只使用Hash特征损失 + Hash正则化损失
   📊 简化训练参数统计:
      - 主要模型: 2.36M
      - 偏移预测器: 0.96M
      - Hash编码器: 0.13M
      - 总计: 3.46M
   📈 统一学习率: 0.0001
   ✅ 优化器已切换到简化Hash训练模式（等价wrap版本）
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [01:13<00:00,  2.33it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:00<00:00, 16.21it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-0, seen -- Mean err: 5.97, Acc: 0.96, Rec : 0.98, Class and Pose  : 0.95
Validation time for epoch 0: 2.26 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4874/4874 [04:40<00:00, 17.35it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:33<00:00,  2.30it/s]
Epoch-0, unseen -- Mean err: 7.43, Acc: 0.90, Rec : 0.99, Class and Pose  : 0.90
Validation time for epoch 0: 5.27 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4065/4065 [03:42<00:00, 18.31it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-0, seen_occ -- Mean err: 13.33, Acc: 0.81, Rec : 0.91, Class and Pose  : 0.79
Validation time for epoch 0: 4.29 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:06<00:00, 17.32it/s]
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 621/621 [1:01:32<00:00,  5.95s/it]
Epoch-0, unseen_occ -- Mean err: 19.64, Acc: 0.69, Rec : 0.87, Class and Pose  : 0.68
Validation time for epoch 0: 4.70 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [01:13<00:00,  2.32it/s]5s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:58<00:00, 16.67it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-1, seen -- Mean err: 6.62, Acc: 0.94, Rec : 0.98, Class and Pose  : 0.94
Validation time for epoch 1: 2.24 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4874/4874 [04:31<00:00, 17.92it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-1, unseen -- Mean err: 8.16, Acc: 0.89, Rec : 0.99, Class and Pose  : 0.89
Validation time for epoch 1: 5.12 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4065/4065 [03:39<00:00, 18.55it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:32<00:00,  2.32it/s]
Epoch-1, seen_occ -- Mean err: 15.52, Acc: 0.77, Rec : 0.87, Class and Pose  : 0.75
Validation time for epoch 1: 4.24 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [03:50<00:00, 18.52it/s]
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 621/621 [1:01:21<00:00,  5.93s/it]
Epoch-1, unseen_occ -- Mean err: 23.81, Acc: 0.61, Rec : 0.81, Class and Pose  : 0.60
Validation time for epoch 1: 4.42 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [01:12<00:00,  2.33it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:59<00:00, 16.49it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-2, seen -- Mean err: 5.51, Acc: 0.97, Rec : 0.99, Class and Pose  : 0.97
Validation time for epoch 2: 2.25 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4874/4874 [04:31<00:00, 17.96it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:32<00:00,  2.33it/s]
Epoch-2, unseen -- Mean err: 7.60, Acc: 0.91, Rec : 1.00, Class and Pose  : 0.91
Validation time for epoch 2: 5.11 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4065/4065 [03:41<00:00, 18.39it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-2, seen_occ -- Mean err: 14.64, Acc: 0.79, Rec : 0.87, Class and Pose  : 0.77
Validation time for epoch 2: 4.27 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [03:50<00:00, 18.53it/s]
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 621/621 [1:01:19<00:00,  5.93s/it]
Epoch-2, unseen_occ -- Mean err: 23.61, Acc: 0.61, Rec : 0.80, Class and Pose  : 0.61
Validation time for epoch 2: 4.43 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [01:12<00:00,  2.33it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:58<00:00, 16.70it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-3, seen -- Mean err: 6.14, Acc: 0.96, Rec : 0.98, Class and Pose  : 0.96
Validation time for epoch 3: 2.23 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4874/4874 [04:36<00:00, 17.65it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-3, unseen -- Mean err: 8.39, Acc: 0.89, Rec : 0.99, Class and Pose  : 0.89
Validation time for epoch 3: 5.19 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4065/4065 [03:39<00:00, 18.53it/s]
处理模板数据:   5%|█████▊                                                                                                        | 4/76 [7:21:53<132:34:07, 6628.43s/it]
Epoch-3, seen_occ -- Mean err: 16.80, Acc: 0.75, Rec : 0.86, Class and Pose  : 0.74
Validation time for epoch 3: 4.24 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
 12%|██████████████▊                                                                                                            | 3/25 [12:28:48<91:31:13, 14976.07s/it]
Traceback (most recent call last):
  File "train_new.py", line 443, in <module>
    main()
  File "train_new.py", line 363, in main
    testing_score = testing_utils_dino.test(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 87, in test
    for miniBatch in tqdm(template_dataloader, desc="处理模板数据"):
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/std.py", line 1181, in __iter__
    for obj in iterable:
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 681, in __next__
    data = self._next_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1359, in _next_data
    idx, data = self._get_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1315, in _get_data
    success, data = self._try_get_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1163, in _try_get_data
    data = self._data_queue.get(timeout=timeout)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/queue.py", line 179, in get
    self.not_empty.wait(remaining)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/threading.py", line 306, in wait
    gotit = waiter.acquire(True, timeout)
KeyboardInterrupt
