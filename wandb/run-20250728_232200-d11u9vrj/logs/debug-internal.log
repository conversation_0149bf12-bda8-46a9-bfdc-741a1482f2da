{"time":"2025-07-28T23:22:00.552267113+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250728_232200-d11u9vrj/logs/debug-core.log"}
{"time":"2025-07-28T23:22:00.672873472+08:00","level":"INFO","msg":"created new stream","id":"d11u9vrj"}
{"time":"2025-07-28T23:22:00.67292927+08:00","level":"INFO","msg":"stream: started","id":"d11u9vrj"}
{"time":"2025-07-28T23:22:00.673012844+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"d11u9vrj"}
{"time":"2025-07-28T23:22:00.673065291+08:00","level":"INFO","msg":"handler: started","stream_id":"d11u9vrj"}
{"time":"2025-07-28T23:22:00.673022649+08:00","level":"INFO","msg":"sender: started","stream_id":"d11u9vrj"}
{"time":"2025-07-28T23:22:02.145823656+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-29T00:02:53.919114686+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-728/d11u9vrj/file_stream\": EOF"}
{"time":"2025-07-29T00:48:23.919821171+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-728/d11u9vrj/file_stream\": EOF"}
{"time":"2025-07-29T00:48:31.150118345+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-728/d11u9vrj/file_stream\": EOF"}
{"time":"2025-07-29T02:34:38.919858492+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-728/d11u9vrj/file_stream\": EOF"}
{"time":"2025-07-29T02:38:38.919991148+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-728/d11u9vrj/file_stream\": EOF"}
{"time":"2025-07-29T04:37:53.919985258+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-728/d11u9vrj/file_stream\": EOF"}
{"time":"2025-07-29T04:43:23.266338715+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-728/d11u9vrj/file_stream\": local error: tls: bad record MAC"}
{"time":"2025-07-29T04:43:33.027071559+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/graphql","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-07-29T09:58:32.257299978+08:00","level":"INFO","msg":"stream: closing","id":"d11u9vrj"}
{"time":"2025-07-29T09:58:32.257332463+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-29T09:58:32.309108781+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-29T09:58:34.170650878+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-29T09:58:40.493783545+08:00","level":"INFO","msg":"handler: closed","stream_id":"d11u9vrj"}
{"time":"2025-07-29T09:58:40.493841897+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"d11u9vrj"}
{"time":"2025-07-29T09:58:40.493916648+08:00","level":"INFO","msg":"sender: closed","stream_id":"d11u9vrj"}
{"time":"2025-07-29T09:58:40.494004998+08:00","level":"INFO","msg":"stream: closed","id":"d11u9vrj"}
