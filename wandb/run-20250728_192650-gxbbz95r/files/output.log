特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.13M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 1开始
   📊 Hash阶段优化器参数数量: 0.13M
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [28:40<00:00,  2.76s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.78it/s]6s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:36<00:00, 10.16it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.70it/s]
Epoch-0, seen -- Mean err: 4.55, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 2.26 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:07<00:00, 13.19it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.34it/s]
Epoch-0, unseen -- Mean err: 4.21, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 6.46 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:56<00:00, 14.41it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.62it/s]
Epoch-0, seen_occ -- Mean err: 13.20, Acc: 0.81, Rec : 0.93, Class and Pose  : 0.80
Validation time for epoch 0: 5.21 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:40<00:00, 14.80it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:42<00:00,  2.86s/it]
Epoch-0, unseen_occ -- Mean err: 9.23, Acc: 0.91, Rec : 0.98, Class and Pose  : 0.90
Validation time for epoch 0: 2.84 minutes

🔄 Epoch 1: 切换到Hash训练阶段（实验1：简化版本）
   🎯 目标：复现wrap版本的训练方式
   📝 配置：只使用Hash特征损失 + Hash正则化损失
   📊 简化训练参数统计:
      - 主要模型: 1.43M
      - Hash编码器: 0.13M
      - 总计: 1.56M
   📈 统一学习率: 0.0001
   ✅ 优化器已切换到简化Hash训练模式（等价wrap版本）
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:34<00:00,  4.89it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:42<00:00,  9.54it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:15<00:00,  4.79it/s]
Epoch-1, seen -- Mean err: 4.53, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 2.36 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:19<00:00, 12.79it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.53it/s]
Epoch-1, unseen -- Mean err: 4.79, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 6.64 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:06<00:00, 13.96it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.30it/s]
Epoch-1, seen_occ -- Mean err: 14.48, Acc: 0.78, Rec : 0.91, Class and Pose  : 0.77
Validation time for epoch 1: 5.35 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:43<00:00, 14.56it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:19<00:00,  2.82s/it]
Epoch-1, unseen_occ -- Mean err: 12.25, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 1: 2.90 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.79it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:39<00:00,  9.83it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.49it/s]
Epoch-2, seen -- Mean err: 4.50, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 2.35 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:21<00:00, 12.71it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.27it/s]
Epoch-2, unseen -- Mean err: 4.83, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 6.69 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:05<00:00, 13.99it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.64it/s]
Epoch-2, seen_occ -- Mean err: 14.94, Acc: 0.79, Rec : 0.92, Class and Pose  : 0.79
Validation time for epoch 2: 5.35 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:37<00:00, 15.09it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:33<00:00,  2.85s/it]
Epoch-2, unseen_occ -- Mean err: 10.70, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 2: 2.81 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.73it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:39<00:00,  9.82it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:15<00:00,  4.83it/s]
Epoch-3, seen -- Mean err: 4.48, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 2.34 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:33<00:00, 14.55it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.37it/s]
Epoch-3, unseen -- Mean err: 4.67, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 5.88 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:04<00:00, 17.44it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.36it/s]
Epoch-3, seen_occ -- Mean err: 14.57, Acc: 0.79, Rec : 0.90, Class and Pose  : 0.78
Validation time for epoch 3: 4.34 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:12<00:00, 17.92it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:28<00:00,  2.84s/it]
Epoch-3, unseen_occ -- Mean err: 10.89, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 3: 2.40 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.82it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:30<00:00, 10.87it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:17<00:00,  4.42it/s]
Epoch-4, seen -- Mean err: 4.48, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 2.15 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:23<00:00, 14.99it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.14it/s]
Epoch-4, unseen -- Mean err: 4.71, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 5.73 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像:  75%|████████████████████████████████████████████████████████████████████████████████████▎                            | 3189/4272 [03:16<01:06, 16.26it/s]
 16%|████████████████████                                                                                                         | 4/25 [3:44:54<19:40:44, 3373.55s/it]
Traceback (most recent call last):
  File "train_new.py", line 437, in <module>
    main()
  File "train_new.py", line 357, in main
    testing_score = testing_utils_dino.test(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 154, in test
    original_query_features = model(query)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 258, in forward
    cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/new_dino_network.py", line 57, in forward
    _ = self.model(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 325, in forward
    ret = self.forward_features(*args, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 261, in forward_features
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 254, in forward
    return super().forward(x_or_x_list)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 112, in forward
    x = x + attn_residual_func(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 91, in attn_residual_func
    return self.ls1(self.attn(self.norm1(x)))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/attention.py", line 77, in forward
    return super().forward(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/attention.py", line 66, in forward
    x = (attn @ v).transpose(1, 2).reshape(B, N, C)
KeyboardInterrupt
