{"time":"2025-07-06T13:09:35.993734903+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250706_130935-938hz4s5/logs/debug-core.log"}
{"time":"2025-07-06T13:09:36.108036549+08:00","level":"INFO","msg":"created new stream","id":"938hz4s5"}
{"time":"2025-07-06T13:09:36.108124147+08:00","level":"INFO","msg":"stream: started","id":"938hz4s5"}
{"time":"2025-07-06T13:09:36.108177847+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"938hz4s5"}
{"time":"2025-07-06T13:09:36.108366347+08:00","level":"INFO","msg":"handler: started","stream_id":"938hz4s5"}
{"time":"2025-07-06T13:09:36.10839215+08:00","level":"INFO","msg":"sender: started","stream_id":"938hz4s5"}
{"time":"2025-07-06T13:09:36.762777619+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-06T13:12:12.732970232+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T13:20:57.731579184+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T13:27:42.731644462+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T13:48:27.732307956+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T14:45:12.732965386+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T15:18:57.73177076+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T15:26:42.730827843+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T15:28:27.730424254+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T15:33:12.732666818+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T16:04:42.730451584+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T16:54:27.730248718+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T18:40:12.732030463+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T18:40:27.731155351+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T18:47:27.73136347+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T19:31:42.730929115+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
{"time":"2025-07-06T19:32:12.731190045+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-706/938hz4s5/file_stream\": EOF"}
