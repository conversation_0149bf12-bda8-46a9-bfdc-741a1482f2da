特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 1.43M
100%|████████████████████████████████████████████| 623/623 [31:27<00:00,  3.03s/it]
处理模板数据: 100%|██████████████████████████████| 170/170 [00:38<00:00,  4.44it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:23<00:00, 11.75it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:17<00:00,  4.32it/s]
Epoch-0, seen -- Mean err: 5.21, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 2.07 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [06:32<00:00, 12.36it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:17<00:00,  4.25it/s]
Epoch-0, unseen -- Mean err: 4.70, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 6.87 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [05:43<00:00, 12.44it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:09<00:00,  4.07it/s]
Epoch-0, seen_occ -- Mean err: 14.51, Acc: 0.82, Rec : 0.93, Class and Pose  : 0.82
Validation time for epoch 0: 6.07 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [03:09<00:00, 12.54it/s]
100%|████████████████████████████████████████████| 623/623 [30:56<00:00,  2.98s/it]
Epoch-0, unseen_occ -- Mean err: 10.86, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.85
Validation time for epoch 0: 3.36 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:38<00:00,  4.47it/s]3s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:20<00:00, 12.17it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:17<00:00,  4.30it/s]
Epoch-1, seen -- Mean err: 5.27, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 2.02 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [06:07<00:00, 13.18it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:17<00:00,  4.41it/s]
Epoch-1, unseen -- Mean err: 5.17, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 6.46 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [05:28<00:00, 13.02it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:09<00:00,  4.12it/s]
Epoch-1, seen_occ -- Mean err: 17.95, Acc: 0.76, Rec : 0.88, Class and Pose  : 0.75
Validation time for epoch 1: 5.80 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:54<00:00, 13.66it/s]
100%|████████████████████████████████████████████| 623/623 [24:28<00:00,  2.36s/it]
Epoch-1, unseen_occ -- Mean err: 15.43, Acc: 0.80, Rec : 0.96, Class and Pose  : 0.80
Validation time for epoch 1: 3.09 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:27<00:00,  6.15it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:58<00:00, 16.73it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  5.92it/s]
Epoch-2, seen -- Mean err: 5.35, Acc: 0.99, Rec : 0.99, Class and Pose  : 0.98
Validation time for epoch 2: 1.48 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:29<00:00, 17.99it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  5.99it/s]
Epoch-2, unseen -- Mean err: 5.37, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 2: 4.74 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:52<00:00, 14.62it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.24it/s]
Epoch-2, seen_occ -- Mean err: 19.03, Acc: 0.74, Rec : 0.86, Class and Pose  : 0.73
Validation time for epoch 2: 5.13 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:55<00:00, 13.51it/s]
100%|████████████████████████████████████████████| 623/623 [30:37<00:00,  2.95s/it]
Epoch-2, unseen_occ -- Mean err: 16.21, Acc: 0.78, Rec : 0.96, Class and Pose  : 0.77
Validation time for epoch 2: 3.12 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:37<00:00,  4.52it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:24<00:00, 11.58it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:17<00:00,  4.40it/s]
Epoch-3, seen -- Mean err: 5.05, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 2.08 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [06:19<00:00, 12.77it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:17<00:00,  4.38it/s]
Epoch-3, unseen -- Mean err: 5.19, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 3: 6.66 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [05:36<00:00, 12.69it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:09<00:00,  4.06it/s]
Epoch-3, seen_occ -- Mean err: 19.01, Acc: 0.74, Rec : 0.86, Class and Pose  : 0.74
Validation time for epoch 3: 5.93 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:55<00:00, 13.54it/s]
100%|████████████████████████████████████████████| 623/623 [30:48<00:00,  2.97s/it]
Epoch-3, unseen_occ -- Mean err: 15.37, Acc: 0.80, Rec : 0.95, Class and Pose  : 0.80
Validation time for epoch 3: 3.12 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:37<00:00,  4.50it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:15<00:00, 12.94it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:17<00:00,  4.46it/s]
Epoch-4, seen -- Mean err: 4.79, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 1.93 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [06:35<00:00, 12.26it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:17<00:00,  4.32it/s]
Epoch-4, unseen -- Mean err: 5.18, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 4: 6.90 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [06:04<00:00, 11.72it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:09<00:00,  4.20it/s]
Epoch-4, seen_occ -- Mean err: 18.70, Acc: 0.74, Rec : 0.87, Class and Pose  : 0.74
Validation time for epoch 4: 6.42 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:54<00:00, 13.59it/s]
 61%|██████████████████████████▋                 | 378/623 [15:06<09:47,  2.40s/it]
Epoch-4, unseen_occ -- Mean err: 14.23, Acc: 0.81, Rec : 0.96, Class and Pose  : 0.81
Validation time for epoch 4: 3.11 minutes
 20%|████████                                | 5/25 [4:10:18<16:41:14, 3003.72s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
Traceback (most recent call last):
  File "train_new.py", line 443, in <module>
    main()
  File "train_new.py", line 328, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 173, in train
    neg_features = model(neg)  # 返回字典
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 281, in forward
    features = self.aggregation_network(cls_tokens, patch_tokens, mask)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 170, in forward
    pose_spatial = self.spatial_projection(fused_spatial)  # [B, descriptor_size, H, W]
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 45, in forward
    out = self.conv1(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/container.py", line 139, in forward
    input = module(input)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/conv.py", line 457, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/conv.py", line 453, in _conv_forward
    return F.conv2d(input, weight, bias, self.stride,
KeyboardInterrupt
