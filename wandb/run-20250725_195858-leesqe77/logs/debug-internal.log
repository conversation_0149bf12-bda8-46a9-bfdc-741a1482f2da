{"time":"2025-07-25T19:58:58.209431208+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250725_195858-leesqe77/logs/debug-core.log"}
{"time":"2025-07-25T19:58:58.448599626+08:00","level":"INFO","msg":"created new stream","id":"leesqe77"}
{"time":"2025-07-25T19:58:58.448656875+08:00","level":"INFO","msg":"stream: started","id":"leesqe77"}
{"time":"2025-07-25T19:58:58.448722035+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"leesqe77"}
{"time":"2025-07-25T19:58:58.448873774+08:00","level":"INFO","msg":"handler: started","stream_id":"leesqe77"}
{"time":"2025-07-25T19:58:58.448895758+08:00","level":"INFO","msg":"sender: started","stream_id":"leesqe77"}
{"time":"2025-07-25T19:58:59.32283772+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-25T20:22:51.238956633+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/leesqe77/file_stream\": unexpected EOF"}
{"time":"2025-07-25T20:25:37.445107466+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/leesqe77/file_stream\": unexpected EOF"}
{"time":"2025-07-25T21:19:09.422180128+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/leesqe77/file_stream\": unexpected EOF"}
{"time":"2025-07-25T21:21:35.923853526+08:00","level":"ERROR","msg":"sender: sendStopStatus: failed to get run stopped status: net/http: request canceled (Client.Timeout or context cancellation while reading body)"}
{"time":"2025-07-25T21:34:51.161515387+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
