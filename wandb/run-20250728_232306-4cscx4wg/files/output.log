特征提取器可训练参数数量: 1.43M
偏移量预测器可训练参数数量: 0.96M
✅ 偏移量预测功能已启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 2.39M
100%|████████████████████████████████████████████| 623/623 [26:50<00:00,  2.58s/it]
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.60it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:07<00:00, 14.58it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.49it/s]
Epoch-0, seen -- Mean err: 4.53, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 1.76 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:52<00:00, 16.56it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.54it/s]
Epoch-0, unseen -- Mean err: 4.23, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 5.20 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:20<00:00, 16.41it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.53it/s]
Epoch-0, seen_occ -- Mean err: 13.34, Acc: 0.80, Rec : 0.93, Class and Pose  : 0.80
Validation time for epoch 0: 4.64 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:31<00:00, 15.72it/s]
100%|████████████████████████████████████████████| 623/623 [25:44<00:00,  2.48s/it]
Epoch-0, unseen_occ -- Mean err: 9.29, Acc: 0.90, Rec : 0.98, Class and Pose  : 0.90
Validation time for epoch 0: 2.69 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:37<00:00,  4.57it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:07<00:00, 14.44it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.59it/s]
Epoch-1, seen -- Mean err: 4.37, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 1.78 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:51<00:00, 16.62it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.53it/s]
Epoch-1, unseen -- Mean err: 4.53, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 5.16 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:18<00:00, 16.54it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.47it/s]
Epoch-1, seen_occ -- Mean err: 14.12, Acc: 0.79, Rec : 0.92, Class and Pose  : 0.79
Validation time for epoch 1: 4.60 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:15<00:00, 17.49it/s]
100%|████████████████████████████████████████████| 623/623 [25:27<00:00,  2.45s/it]
Epoch-1, unseen_occ -- Mean err: 10.36, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 1: 2.43 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.63it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:04<00:00, 15.10it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.54it/s]
Epoch-2, seen -- Mean err: 4.31, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 1.73 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:49<00:00, 16.76it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.57it/s]
Epoch-2, unseen -- Mean err: 4.52, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 5.13 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [05:25<00:00, 13.12it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.41it/s]
Epoch-2, seen_occ -- Mean err: 15.44, Acc: 0.78, Rec : 0.90, Class and Pose  : 0.77
Validation time for epoch 2: 5.73 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:15<00:00, 17.50it/s]
100%|████████████████████████████████████████████| 623/623 [25:13<00:00,  2.43s/it]
Epoch-2, unseen_occ -- Mean err: 10.86, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 2: 2.44 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.61it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:05<00:00, 15.03it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.58it/s]
Epoch-3, seen -- Mean err: 4.30, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 3: 1.73 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:48<00:00, 16.79it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.51it/s]
Epoch-3, unseen -- Mean err: 4.69, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 5.11 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:14<00:00, 16.81it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.58it/s]
Epoch-3, seen_occ -- Mean err: 14.66, Acc: 0.77, Rec : 0.90, Class and Pose  : 0.77
Validation time for epoch 3: 4.55 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:11<00:00, 18.08it/s]
100%|████████████████████████████████████████████| 623/623 [24:58<00:00,  2.41s/it]
Epoch-3, unseen_occ -- Mean err: 11.00, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 3: 2.36 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.62it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:06<00:00, 14.73it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.60it/s]
Epoch-4, seen -- Mean err: 4.21, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 4: 1.75 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:48<00:00, 16.82it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.52it/s]
Epoch-4, unseen -- Mean err: 4.59, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 5.10 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:14<00:00, 16.79it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.40it/s]
Epoch-4, seen_occ -- Mean err: 15.72, Acc: 0.76, Rec : 0.90, Class and Pose  : 0.76
Validation time for epoch 4: 4.54 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:12<00:00, 17.97it/s]
100%|████████████████████████████████████████████| 623/623 [25:23<00:00,  2.45s/it]
Epoch-4, unseen_occ -- Mean err: 10.90, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 4: 2.38 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:35<00:00,  4.77it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:09<00:00, 14.19it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.50it/s]
Epoch-5, seen -- Mean err: 4.23, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 5: 1.78 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:59<00:00, 16.20it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.59it/s]
Epoch-5, unseen -- Mean err: 4.52, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 5: 5.31 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:14<00:00, 16.76it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.40it/s]
Epoch-5, seen_occ -- Mean err: 15.81, Acc: 0.75, Rec : 0.89, Class and Pose  : 0.75
Validation time for epoch 5: 4.55 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:18<00:00, 17.11it/s]
100%|████████████████████████████████████████████| 623/623 [25:26<00:00,  2.45s/it]
Epoch-5, unseen_occ -- Mean err: 10.77, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 5: 2.48 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.63it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:06<00:00, 14.83it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.56it/s]
Epoch-6, seen -- Mean err: 4.26, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 6: 1.75 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:51<00:00, 16.61it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.55it/s]
Epoch-6, unseen -- Mean err: 4.69, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 6: 5.17 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:16<00:00, 16.67it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.25it/s]
Epoch-6, seen_occ -- Mean err: 15.53, Acc: 0.75, Rec : 0.89, Class and Pose  : 0.75
Validation time for epoch 6: 4.58 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:25<00:00, 16.30it/s]
100%|████████████████████████████████████████████| 623/623 [25:00<00:00,  2.41s/it]
Epoch-6, unseen_occ -- Mean err: 10.94, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 6: 2.64 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.63it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:05<00:00, 14.92it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.60it/s]
Epoch-7, seen -- Mean err: 4.20, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 7: 1.73 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:52<00:00, 16.56it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.57it/s]
Epoch-7, unseen -- Mean err: 4.49, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 7: 5.19 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:20<00:00, 16.38it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.49it/s]
Epoch-7, seen_occ -- Mean err: 16.61, Acc: 0.75, Rec : 0.88, Class and Pose  : 0.75
Validation time for epoch 7: 4.66 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:26<00:00, 16.19it/s]
100%|████████████████████████████████████████████| 623/623 [24:59<00:00,  2.41s/it]
Epoch-7, unseen_occ -- Mean err: 10.52, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 7: 2.61 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.63it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:05<00:00, 15.04it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.47it/s]
Epoch-8, seen -- Mean err: 4.27, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 8: 1.72 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:57<00:00, 16.28it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.59it/s]
Epoch-8, unseen -- Mean err: 4.53, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 8: 5.28 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:13<00:00, 16.82it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.54it/s]
Epoch-8, seen_occ -- Mean err: 16.62, Acc: 0.75, Rec : 0.88, Class and Pose  : 0.75
Validation time for epoch 8: 4.54 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:17<00:00, 17.30it/s]
100%|████████████████████████████████████████████| 623/623 [25:02<00:00,  2.41s/it]
Epoch-8, unseen_occ -- Mean err: 10.44, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 8: 2.46 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.62it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:06<00:00, 14.83it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.58it/s]
Epoch-9, seen -- Mean err: 4.24, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 9: 1.74 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:51<00:00, 16.61it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.59it/s]
Epoch-9, unseen -- Mean err: 4.33, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 9: 5.17 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:12<00:00, 16.93it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.42it/s]
Epoch-9, seen_occ -- Mean err: 15.15, Acc: 0.77, Rec : 0.91, Class and Pose  : 0.76
Validation time for epoch 9: 4.51 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:11<00:00, 18.04it/s]
100%|████████████████████████████████████████████| 623/623 [25:35<00:00,  2.47s/it]
Epoch-9, unseen_occ -- Mean err: 9.69, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 9: 2.37 minutes
   📉 学习率已衰减，当前学习率: 2e-05
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.64it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:05<00:00, 14.95it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.57it/s]
Epoch-10, seen -- Mean err: 4.24, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 10: 1.73 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:49<00:00, 16.74it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.54it/s]
Epoch-10, unseen -- Mean err: 4.55, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 10: 5.13 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:13<00:00, 16.87it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.39it/s]
Epoch-10, seen_occ -- Mean err: 16.20, Acc: 0.74, Rec : 0.88, Class and Pose  : 0.73
Validation time for epoch 10: 4.52 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [01:58<00:00, 20.13it/s]
100%|████████████████████████████████████████████| 623/623 [25:44<00:00,  2.48s/it]
Epoch-10, unseen_occ -- Mean err: 10.88, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 10: 2.14 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.65it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:05<00:00, 14.91it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.56it/s]
Epoch-11, seen -- Mean err: 4.17, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 11: 1.73 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:56<00:00, 16.36it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.57it/s]
Epoch-11, unseen -- Mean err: 4.58, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 11: 5.25 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:36<00:00, 15.46it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.52it/s]
Epoch-11, seen_occ -- Mean err: 16.09, Acc: 0.74, Rec : 0.88, Class and Pose  : 0.74
Validation time for epoch 11: 4.91 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [01:39<00:00, 24.00it/s]
100%|████████████████████████████████████████████| 623/623 [25:54<00:00,  2.50s/it]
Epoch-11, unseen_occ -- Mean err: 11.09, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 11: 1.82 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.60it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:06<00:00, 14.78it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.54it/s]
Epoch-12, seen -- Mean err: 4.10, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 12: 1.75 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:48<00:00, 16.80it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.64it/s]
Epoch-12, unseen -- Mean err: 4.48, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 12: 5.11 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:10<00:00, 17.04it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:07<00:00,  4.87it/s]
Epoch-12, seen_occ -- Mean err: 17.02, Acc: 0.74, Rec : 0.87, Class and Pose  : 0.74
Validation time for epoch 12: 4.47 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [01:34<00:00, 25.28it/s]
100%|████████████████████████████████████████████| 623/623 [25:15<00:00,  2.43s/it]
Epoch-12, unseen_occ -- Mean err: 10.81, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 12: 1.73 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.63it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:07<00:00, 14.49it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.57it/s]
Epoch-13, seen -- Mean err: 4.08, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 13: 1.77 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:46<00:00, 16.94it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.55it/s]
Epoch-13, unseen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 13: 5.08 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:18<00:00, 16.51it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.45it/s]
Epoch-13, seen_occ -- Mean err: 16.30, Acc: 0.74, Rec : 0.88, Class and Pose  : 0.74
Validation time for epoch 13: 4.62 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [01:56<00:00, 20.35it/s]
100%|████████████████████████████████████████████| 623/623 [25:28<00:00,  2.45s/it]
Epoch-13, unseen_occ -- Mean err: 11.05, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 13: 2.12 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.64it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:05<00:00, 14.97it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.54it/s]
Epoch-14, seen -- Mean err: 4.11, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 14: 1.73 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [05:04<00:00, 15.94it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.55it/s]
Epoch-14, unseen -- Mean err: 4.58, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 14: 5.37 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:17<00:00, 16.57it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:08<00:00,  4.42it/s]
Epoch-14, seen_occ -- Mean err: 16.25, Acc: 0.75, Rec : 0.86, Class and Pose  : 0.74
Validation time for epoch 14: 4.60 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [01:54<00:00, 20.71it/s]
100%|████████████████████████████████████████████| 623/623 [25:54<00:00,  2.49s/it]
Epoch-14, unseen_occ -- Mean err: 10.95, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 14: 2.08 minutes
   📉 学习率已衰减，当前学习率: 4.000000000000001e-06
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.62it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:06<00:00, 14.86it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.61it/s]
Epoch-15, seen -- Mean err: 4.08, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 15: 1.74 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [04:47<00:00, 16.85it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.59it/s]
Epoch-15, unseen -- Mean err: 4.55, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 15: 5.09 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:27<00:00, 15.98it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:07<00:00,  5.01it/s]
Epoch-15, seen_occ -- Mean err: 16.37, Acc: 0.74, Rec : 0.88, Class and Pose  : 0.73
Validation time for epoch 15: 4.75 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:03<00:00, 19.20it/s]
 14%|██████▌                                      | 90/623 [03:19<19:40,  2.21s/it]
Epoch-15, unseen_occ -- Mean err: 11.02, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 15: 2.24 minutes
 64%|████████████████████████▉              | 16/25 [10:35:26<5:57:26, 2382.90s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
Traceback (most recent call last):
  File "train_new.py", line 437, in <module>
    main()
  File "train_new.py", line 322, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 173, in train
    neg_features = model(neg)  # 返回字典
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 258, in forward
    cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/new_dino_network.py", line 57, in forward
    _ = self.model(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 325, in forward
    ret = self.forward_features(*args, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 261, in forward_features
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 254, in forward
    return super().forward(x_or_x_list)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 112, in forward
    x = x + attn_residual_func(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 91, in attn_residual_func
    return self.ls1(self.attn(self.norm1(x)))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/attention.py", line 77, in forward
    return super().forward(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/attention.py", line 66, in forward
    x = (attn @ v).transpose(1, 2).reshape(B, N, C)
KeyboardInterrupt
