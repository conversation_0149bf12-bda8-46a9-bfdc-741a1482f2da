特征提取器可训练参数数量: 3.21M
偏移量预测器可训练参数数量: 0.96M
✅ 偏移量预测功能已启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 4.18M
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [1:43:04<00:00,  9.93s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [02:31<00:00,  1.12it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [02:17<00:00,  7.15it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [01:07<00:00,  1.13it/s]
Epoch-0, seen -- Mean err: 5.20, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 4.87 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [10:44<00:00,  7.52it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [01:07<00:00,  1.12it/s]
Epoch-0, unseen -- Mean err: 4.64, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 11.91 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [07:49<00:00,  9.09it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:22<00:00,  1.67it/s]
Epoch-0, seen_occ -- Mean err: 13.57, Acc: 0.83, Rec : 0.94, Class and Pose  : 0.83
Validation time for epoch 0: 9.01 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:22<00:00, 11.73it/s]
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [1:42:18<00:00,  9.85s/it]
Epoch-0, unseen_occ -- Mean err: 10.15, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 0: 3.80 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [01:38<00:00,  1.73it/s]5s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:53<00:00,  8.62it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [01:04<00:00,  1.18it/s]
Epoch-1, seen -- Mean err: 5.73, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 3.58 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [10:50<00:00,  7.45it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [01:05<00:00,  1.16it/s]
Epoch-1, unseen -- Mean err: 5.26, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 11.97 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像:   9%|█████████▊                                                                                                        | 368/4272 [00:44<07:48,  8.33it/s]
  4%|████▉                                                                                                                      | 1/25 [4:12:32<101:01:04, 15152.70s/it]
Traceback (most recent call last):
  File "train_new.py", line 443, in <module>
    main()
  File "train_new.py", line 363, in main
    testing_score = testing_utils_dino.test(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 154, in test
    original_query_features = model(query)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 860, in forward
    cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/new_dino_network.py", line 57, in forward
    _ = self.model(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 325, in forward
    ret = self.forward_features(*args, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 261, in forward_features
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 254, in forward
    return super().forward(x_or_x_list)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 113, in forward
    x = x + ffn_residual_func(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 94, in ffn_residual_func
    return self.ls2(self.mlp(self.norm2(x)))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/mlp.py", line 35, in forward
    x = self.fc1(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/linear.py", line 114, in forward
    return F.linear(input, self.weight, self.bias)
KeyboardInterrupt
