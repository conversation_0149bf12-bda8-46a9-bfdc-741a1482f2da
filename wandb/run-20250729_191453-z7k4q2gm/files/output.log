特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 1.43M
100%|████████████████████████████████████████████| 554/554 [16:09<00:00,  1.75s/it]
处理模板数据: 100%|██████████████████████████████| 151/151 [00:19<00:00,  7.89it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 872/872 [00:49<00:00, 17.51it/s]
处理模板数据: 100%|████████████████████████████████| 95/95 [00:11<00:00,  8.02it/s]
Epoch-0, seen -- Mean err: 4.66, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 1.21 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 6061/6061 [04:28<00:00, 22.57it/s]
处理模板数据: 100%|██████████████████████████████| 113/113 [00:12<00:00,  9.12it/s]
Epoch-0, unseen -- Mean err: 5.68, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 0: 4.73 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 6649/6649 [04:57<00:00, 22.38it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:05<00:00,  6.69it/s]
Epoch-0, seen_occ -- Mean err: 11.44, Acc: 0.87, Rec : 0.95, Class and Pose  : 0.86
Validation time for epoch 0: 5.20 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 0it [00:00, ?it/s]███████████████████| 38/38 [00:05<00:00, 10.07it/s]
100%|████████████████████████████████████████████| 554/554 [15:11<00:00,  1.65s/it]
Epoch-0, unseen_occ -- Mean err: 0.00, Acc: 0.00, Rec : 0.00, Class and Pose  : 0.00
Validation time for epoch 0: 0.14 minutes
处理模板数据: 100%|██████████████████████████████| 151/151 [00:17<00:00,  8.72it/s]3s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 872/872 [00:36<00:00, 24.03it/s]
处理模板数据: 100%|████████████████████████████████| 95/95 [00:11<00:00,  8.18it/s]
Epoch-1, seen -- Mean err: 4.91, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 0.94 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 6061/6061 [03:49<00:00, 26.36it/s]
处理模板数据: 100%|██████████████████████████████| 113/113 [00:12<00:00,  9.08it/s]
Epoch-1, unseen -- Mean err: 7.14, Acc: 0.94, Rec : 0.99, Class and Pose  : 0.94
Validation time for epoch 1: 4.07 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 6649/6649 [03:53<00:00, 28.42it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:04<00:00,  8.03it/s]
Epoch-1, seen_occ -- Mean err: 14.89, Acc: 0.80, Rec : 0.91, Class and Pose  : 0.80
Validation time for epoch 1: 4.14 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 0it [00:00, ?it/s]██████████████████▏| 37/38 [00:04<00:00, 10.27it/s]
100%|████████████████████████████████████████████| 554/554 [19:02<00:00,  2.06s/it]
Epoch-1, unseen_occ -- Mean err: 0.00, Acc: 0.00, Rec : 0.00, Class and Pose  : 0.00
Validation time for epoch 1: 0.12 minutes
处理模板数据: 100%|██████████████████████████████| 151/151 [00:17<00:00,  8.87it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 872/872 [00:31<00:00, 27.39it/s]
处理模板数据: 100%|████████████████████████████████| 95/95 [00:10<00:00,  8.82it/s]
Epoch-2, seen -- Mean err: 4.70, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 0.85 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 6061/6061 [03:12<00:00, 31.45it/s]
处理模板数据: 100%|██████████████████████████████| 113/113 [00:12<00:00,  9.13it/s]
Epoch-2, unseen -- Mean err: 6.42, Acc: 0.96, Rec : 0.99, Class and Pose  : 0.96
Validation time for epoch 2: 3.43 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 6649/6649 [03:53<00:00, 28.53it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:05<00:00,  6.88it/s]
Epoch-2, seen_occ -- Mean err: 14.48, Acc: 0.82, Rec : 0.91, Class and Pose  : 0.81
Validation time for epoch 2: 4.13 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 0it [00:00, ?it/s]███████████████████| 38/38 [00:04<00:00,  9.91it/s]
 12%|████▉                                    | 3/25 [1:19:48<9:46:36, 1599.86s/it]
Epoch-2, unseen_occ -- Mean err: 0.00, Acc: 0.00, Rec : 0.00, Class and Pose  : 0.00
Validation time for epoch 2: 0.14 minutes
 42%|██████████████████▋                         | 235/554 [06:16<08:22,  1.57s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
