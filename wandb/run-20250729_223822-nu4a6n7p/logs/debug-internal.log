{"time":"2025-07-29T22:38:22.32059735+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250729_223822-nu4a6n7p/logs/debug-core.log"}
{"time":"2025-07-29T22:38:22.568641832+08:00","level":"INFO","msg":"created new stream","id":"nu4a6n7p"}
{"time":"2025-07-29T22:38:22.568719335+08:00","level":"INFO","msg":"stream: started","id":"nu4a6n7p"}
{"time":"2025-07-29T22:38:22.56879115+08:00","level":"INFO","msg":"handler: started","stream_id":"nu4a6n7p"}
{"time":"2025-07-29T22:38:22.568826823+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"nu4a6n7p"}
{"time":"2025-07-29T22:38:22.568940843+08:00","level":"INFO","msg":"sender: started","stream_id":"nu4a6n7p"}
{"time":"2025-07-29T22:38:23.572519348+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-29T22:46:14.540068047+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-29T23:09:55.661242775+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": local error: tls: bad record MAC"}
{"time":"2025-07-29T23:21:44.542288563+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-29T23:21:59.541664903+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-29T23:34:29.54203797+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-29T23:34:36.82151423+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-30T02:58:14.54136885+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-30T05:29:29.540138943+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-30T05:29:37.052522165+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-30T07:40:59.54044842+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-30T09:45:14.540396105+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-30T09:45:21.943602623+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-30T09:45:31.975977223+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-30T09:45:45.847206432+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/nu4a6n7p/file_stream\": EOF"}
{"time":"2025-07-30T11:08:24.186366922+08:00","level":"INFO","msg":"stream: closing","id":"nu4a6n7p"}
{"time":"2025-07-30T11:08:24.186423374+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-30T11:08:24.1874093+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-30T11:08:25.809566875+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-30T11:08:27.633040647+08:00","level":"INFO","msg":"handler: closed","stream_id":"nu4a6n7p"}
{"time":"2025-07-30T11:08:27.633104355+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"nu4a6n7p"}
{"time":"2025-07-30T11:08:27.63314411+08:00","level":"INFO","msg":"sender: closed","stream_id":"nu4a6n7p"}
{"time":"2025-07-30T11:08:27.633219635+08:00","level":"INFO","msg":"stream: closed","id":"nu4a6n7p"}
