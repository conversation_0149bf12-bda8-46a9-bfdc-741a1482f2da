特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 1.43M
100%|████████████████████████████████████████████| 621/621 [28:56<00:00,  2.80s/it]
处理模板数据: 100%|██████████████████████████████| 170/170 [00:26<00:00,  6.36it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:48<00:00, 20.12it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.27it/s]
Epoch-0, seen -- Mean err: 4.87, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 1.30 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [03:36<00:00, 22.56it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.03it/s]
Epoch-0, unseen -- Mean err: 4.68, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 3.84 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [02:57<00:00, 22.86it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:11<00:00,  6.40it/s]
Epoch-0, seen_occ -- Mean err: 10.82, Acc: 0.85, Rec : 0.93, Class and Pose  : 0.84
Validation time for epoch 0: 3.21 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:04<00:00, 17.47it/s]
100%|████████████████████████████████████████████| 621/621 [28:00<00:00,  2.71s/it]
Epoch-0, unseen_occ -- Mean err: 15.55, Acc: 0.79, Rec : 0.92, Class and Pose  : 0.79
Validation time for epoch 0: 4.31 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:26<00:00,  6.39it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:47<00:00, 20.69it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.09it/s]
Epoch-1, seen -- Mean err: 5.05, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 1.27 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [03:31<00:00, 23.04it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.22it/s]
Epoch-1, unseen -- Mean err: 5.11, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 3.78 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [02:49<00:00, 23.93it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:15<00:00,  4.82it/s]
Epoch-1, seen_occ -- Mean err: 12.52, Acc: 0.82, Rec : 0.91, Class and Pose  : 0.81
Validation time for epoch 1: 3.08 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:49<00:00, 14.76it/s]
100%|████████████████████████████████████████████| 621/621 [27:11<00:00,  2.63s/it]
Epoch-1, unseen_occ -- Mean err: 17.86, Acc: 0.74, Rec : 0.88, Class and Pose  : 0.73
Validation time for epoch 1: 5.13 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:25<00:00,  6.65it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:53<00:00, 18.22it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.15it/s]
Epoch-2, seen -- Mean err: 4.95, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 1.36 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [03:27<00:00, 23.46it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.18it/s]
Epoch-2, unseen -- Mean err: 5.23, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 2: 3.72 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [03:33<00:00, 19.07it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:17<00:00,  4.44it/s]
Epoch-2, seen_occ -- Mean err: 12.38, Acc: 0.82, Rec : 0.91, Class and Pose  : 0.81
Validation time for epoch 2: 3.80 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:41<00:00, 15.17it/s]
100%|████████████████████████████████████████████| 621/621 [26:22<00:00,  2.55s/it]
Epoch-2, unseen_occ -- Mean err: 18.07, Acc: 0.74, Rec : 0.88, Class and Pose  : 0.74
Validation time for epoch 2: 5.02 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:26<00:00,  6.35it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:45<00:00, 21.48it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.18it/s]
Epoch-3, seen -- Mean err: 4.94, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 1.24 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [03:19<00:00, 24.40it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:11<00:00,  6.38it/s]
Epoch-3, unseen -- Mean err: 5.17, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 3: 3.57 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [03:57<00:00, 17.12it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.48it/s]
Epoch-3, seen_occ -- Mean err: 12.93, Acc: 0.82, Rec : 0.90, Class and Pose  : 0.81
Validation time for epoch 3: 4.20 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:34<00:00, 15.59it/s]
100%|████████████████████████████████████████████| 621/621 [26:00<00:00,  2.51s/it]
Epoch-3, unseen_occ -- Mean err: 19.85, Acc: 0.70, Rec : 0.86, Class and Pose  : 0.70
Validation time for epoch 3: 4.89 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:26<00:00,  6.31it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:45<00:00, 21.65it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.13it/s]
Epoch-4, seen -- Mean err: 4.68, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 1.24 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [03:24<00:00, 23.82it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.69it/s]
Epoch-4, unseen -- Mean err: 5.20, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 4: 3.65 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [04:18<00:00, 15.75it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.61it/s]
Epoch-4, seen_occ -- Mean err: 12.06, Acc: 0.83, Rec : 0.92, Class and Pose  : 0.82
Validation time for epoch 4: 4.61 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:34<00:00, 15.58it/s]
100%|████████████████████████████████████████████| 621/621 [25:29<00:00,  2.46s/it]
Epoch-4, unseen_occ -- Mean err: 18.01, Acc: 0.74, Rec : 0.88, Class and Pose  : 0.73
Validation time for epoch 4: 4.88 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:27<00:00,  6.29it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:46<00:00, 21.18it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.15it/s]
Epoch-5, seen -- Mean err: 4.55, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 5: 1.26 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [03:56<00:00, 20.60it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:17<00:00,  4.40it/s]
Epoch-5, unseen -- Mean err: 5.04, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 5: 4.18 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [04:17<00:00, 15.77it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.50it/s]
Epoch-5, seen_occ -- Mean err: 12.05, Acc: 0.83, Rec : 0.92, Class and Pose  : 0.82
Validation time for epoch 5: 4.63 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:35<00:00, 15.49it/s]
100%|████████████████████████████████████████████| 621/621 [24:58<00:00,  2.41s/it]
Epoch-5, unseen_occ -- Mean err: 17.65, Acc: 0.75, Rec : 0.88, Class and Pose  : 0.74
Validation time for epoch 5: 4.92 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:27<00:00,  6.16it/s]3s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:46<00:00, 21.01it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:11<00:00,  6.37it/s]
Epoch-6, seen -- Mean err: 4.45, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 6: 1.27 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [04:30<00:00, 17.99it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.61it/s]
Epoch-6, unseen -- Mean err: 5.14, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 6: 4.75 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [04:20<00:00, 15.62it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.58it/s]
Epoch-6, seen_occ -- Mean err: 11.57, Acc: 0.84, Rec : 0.92, Class and Pose  : 0.83
Validation time for epoch 6: 4.65 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:32<00:00, 15.67it/s]
100%|████████████████████████████████████████████| 621/621 [24:18<00:00,  2.35s/it]
Epoch-6, unseen_occ -- Mean err: 17.60, Acc: 0.74, Rec : 0.89, Class and Pose  : 0.74
Validation time for epoch 6: 4.86 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:26<00:00,  6.43it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:42<00:00, 22.87it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:08<00:00,  8.62it/s]
Epoch-7, seen -- Mean err: 4.43, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 7: 1.19 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [05:06<00:00, 15.90it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.56it/s]
Epoch-7, unseen -- Mean err: 5.17, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 7: 5.29 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [04:16<00:00, 15.83it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.63it/s]
Epoch-7, seen_occ -- Mean err: 11.79, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 7: 4.59 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:26<00:00, 16.02it/s]
100%|████████████████████████████████████████████| 621/621 [23:40<00:00,  2.29s/it]
Epoch-7, unseen_occ -- Mean err: 17.67, Acc: 0.75, Rec : 0.89, Class and Pose  : 0.74
Validation time for epoch 7: 4.75 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.71it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:08<00:00, 14.42it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.52it/s]
Epoch-8, seen -- Mean err: 4.47, Acc: 1.00, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 8: 1.76 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [05:04<00:00, 16.01it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.53it/s]
Epoch-8, unseen -- Mean err: 5.23, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 8: 5.39 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [04:13<00:00, 16.01it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.53it/s]
Epoch-8, seen_occ -- Mean err: 12.18, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 8: 4.54 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:27<00:00, 15.98it/s]
100%|████████████████████████████████████████████| 621/621 [23:25<00:00,  2.26s/it]
Epoch-8, unseen_occ -- Mean err: 18.16, Acc: 0.74, Rec : 0.87, Class and Pose  : 0.73
Validation time for epoch 8: 4.77 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.68it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:08<00:00, 14.34it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.51it/s]
Epoch-9, seen -- Mean err: 4.34, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 9: 1.78 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [05:05<00:00, 15.98it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.60it/s]
Epoch-9, unseen -- Mean err: 4.97, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 9: 5.40 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [04:16<00:00, 15.86it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.62it/s]
Epoch-9, seen_occ -- Mean err: 11.55, Acc: 0.84, Rec : 0.92, Class and Pose  : 0.84
Validation time for epoch 9: 4.58 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:28<00:00, 15.92it/s]
100%|████████████████████████████████████████████| 621/621 [23:30<00:00,  2.27s/it]
Epoch-9, unseen_occ -- Mean err: 17.50, Acc: 0.75, Rec : 0.89, Class and Pose  : 0.75
Validation time for epoch 9: 4.78 minutes
   📉 学习率已衰减，当前学习率: 2e-05
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.67it/s]5s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:05<00:00, 14.87it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.47it/s]
Epoch-10, seen -- Mean err: 4.28, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 10: 1.74 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [05:04<00:00, 16.03it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.61it/s]
Epoch-10, unseen -- Mean err: 4.92, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 10: 5.39 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [04:14<00:00, 15.99it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.61it/s]
Epoch-10, seen_occ -- Mean err: 11.44, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.84
Validation time for epoch 10: 4.55 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:24<00:00, 16.14it/s]
100%|████████████████████████████████████████████| 621/621 [23:42<00:00,  2.29s/it]
Epoch-10, unseen_occ -- Mean err: 17.28, Acc: 0.76, Rec : 0.89, Class and Pose  : 0.75
Validation time for epoch 10: 4.72 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.61it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:07<00:00, 14.44it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.54it/s]
Epoch-11, seen -- Mean err: 4.29, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 11: 1.78 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [05:05<00:00, 15.95it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.54it/s]
Epoch-11, unseen -- Mean err: 4.87, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 11: 5.41 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [04:15<00:00, 15.88it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.68it/s]
Epoch-11, seen_occ -- Mean err: 11.41, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.84
Validation time for epoch 11: 4.57 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [03:58<00:00, 17.94it/s]
100%|████████████████████████████████████████████| 621/621 [24:15<00:00,  2.34s/it]
Epoch-11, unseen_occ -- Mean err: 16.73, Acc: 0.77, Rec : 0.89, Class and Pose  : 0.76
Validation time for epoch 11: 4.27 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:37<00:00,  4.59it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:06<00:00, 14.77it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.52it/s]
Epoch-12, seen -- Mean err: 4.29, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 12: 1.76 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [05:04<00:00, 15.99it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.58it/s]
Epoch-12, unseen -- Mean err: 4.90, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 12: 5.40 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [04:17<00:00, 15.79it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:17<00:00,  4.42it/s]
Epoch-12, seen_occ -- Mean err: 11.21, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.84
Validation time for epoch 12: 4.60 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [03:25<00:00, 20.79it/s]
100%|████████████████████████████████████████████| 621/621 [24:55<00:00,  2.41s/it]
Epoch-12, unseen_occ -- Mean err: 16.55, Acc: 0.77, Rec : 0.89, Class and Pose  : 0.76
Validation time for epoch 12: 3.75 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:37<00:00,  4.58it/s]6s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:09<00:00, 14.12it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.48it/s]
Epoch-13, seen -- Mean err: 4.27, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 13: 1.81 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [05:11<00:00, 15.64it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.58it/s]
Epoch-13, unseen -- Mean err: 4.84, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 13: 5.51 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [04:05<00:00, 16.59it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  5.95it/s]
Epoch-13, seen_occ -- Mean err: 11.14, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.84
Validation time for epoch 13: 4.39 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [02:58<00:00, 23.98it/s]
100%|████████████████████████████████████████████| 621/621 [25:28<00:00,  2.46s/it]
Epoch-13, unseen_occ -- Mean err: 16.71, Acc: 0.77, Rec : 0.89, Class and Pose  : 0.77
Validation time for epoch 13: 3.22 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.61it/s]5s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:07<00:00, 14.47it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.53it/s]
Epoch-14, seen -- Mean err: 4.25, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 14: 1.79 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [05:02<00:00, 16.13it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.52it/s]
Epoch-14, unseen -- Mean err: 4.84, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 14: 5.36 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [03:39<00:00, 18.49it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.23it/s]
Epoch-14, seen_occ -- Mean err: 11.28, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.84
Validation time for epoch 14: 3.98 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [02:52<00:00, 24.78it/s]
100%|████████████████████████████████████████████| 621/621 [26:02<00:00,  2.52s/it]
Epoch-14, unseen_occ -- Mean err: 16.80, Acc: 0.77, Rec : 0.90, Class and Pose  : 0.77
Validation time for epoch 14: 3.11 minutes
   📉 学习率已衰减，当前学习率: 4.000000000000001e-06
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.68it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:08<00:00, 14.26it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.52it/s]
Epoch-15, seen -- Mean err: 4.25, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 15: 1.79 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [05:05<00:00, 15.96it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.52it/s]
Epoch-15, unseen -- Mean err: 4.83, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 15: 5.40 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [03:07<00:00, 21.69it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.32it/s]
Epoch-15, seen_occ -- Mean err: 11.15, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.84
Validation time for epoch 15: 3.44 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [02:58<00:00, 23.97it/s]
100%|████████████████████████████████████████████| 621/621 [26:22<00:00,  2.55s/it]
Epoch-15, unseen_occ -- Mean err: 16.67, Acc: 0.77, Rec : 0.89, Class and Pose  : 0.77
Validation time for epoch 15: 3.20 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:37<00:00,  4.57it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:07<00:00, 14.49it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.66it/s]
Epoch-16, seen -- Mean err: 4.25, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 16: 1.78 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [05:02<00:00, 16.14it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  5.91it/s]
Epoch-16, unseen -- Mean err: 4.83, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 16: 5.34 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [02:52<00:00, 23.60it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.01it/s]
Epoch-16, seen_occ -- Mean err: 11.16, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.85
Validation time for epoch 16: 3.12 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [02:58<00:00, 23.89it/s]
100%|████████████████████████████████████████████| 621/621 [26:45<00:00,  2.59s/it]
Epoch-16, unseen_occ -- Mean err: 16.62, Acc: 0.77, Rec : 0.90, Class and Pose  : 0.77
Validation time for epoch 16: 3.23 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [00:36<00:00,  4.61it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:07<00:00, 14.43it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:16<00:00,  4.51it/s]
Epoch-17, seen -- Mean err: 4.26, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 17: 1.78 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [04:38<00:00, 17.51it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.25it/s]
Epoch-17, unseen -- Mean err: 4.82, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 17: 4.95 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [02:47<00:00, 24.30it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:12<00:00,  6.27it/s]
Epoch-17, seen_occ -- Mean err: 11.16, Acc: 0.85, Rec : 0.93, Class and Pose  : 0.84
Validation time for epoch 17: 3.02 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [03:03<00:00, 23.27it/s]
 70%|██████████████████████████████▌             | 432/621 [23:48<10:24,  3.31s/it]
Epoch-17, unseen_occ -- Mean err: 16.53, Acc: 0.78, Rec : 0.90, Class and Pose  : 0.77
Validation time for epoch 17: 3.30 minutes
 72%|████████████████████████████           | 18/25 [12:29:59<4:51:39, 2499.96s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
Traceback (most recent call last):
  File "train_new.py", line 443, in <module>
    main()
  File "train_new.py", line 328, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 173, in train
    neg_features = model(neg)  # 返回字典
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 281, in forward
    features = self.aggregation_network(cls_tokens, patch_tokens, mask)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 152, in forward
    spatial_weights = F.softmax(self.spatial_weighting(concat_spatial), dim=1)  # [B, num_layers]
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/container.py", line 139, in forward
    input = module(input)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/pooling.py", line 1179, in forward
    return F.adaptive_avg_pool2d(input, self.output_size)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/functional.py", line 1214, in adaptive_avg_pool2d
    return torch._C._nn.adaptive_avg_pool2d(input, _output_size)
KeyboardInterrupt
