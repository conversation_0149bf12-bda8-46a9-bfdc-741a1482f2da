特征提取器可训练参数数量: 2.36M
偏移量预测器可训练参数数量: 0.96M
✅ 偏移量预测功能已启用
Hash编码器可训练参数数量: 0.13M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.13M
初始阶段优化器参数数量: 3.32M
100%|██████████████████████████████████████████| 623/623 [1:02:36<00:00,  6.03s/it]

🔄 Epoch 0: 切换到Hash训练阶段（实验1：简化版本）
   🎯 目标：复现wrap版本的训练方式
   📝 配置：只使用Hash特征损失 + Hash正则化损失
   📊 简化训练参数统计:
      - 主要模型: 2.36M
      - 偏移预测器: 0.96M
      - Hash编码器: 0.13M
      - 总计: 3.46M
   📈 统一学习率: 0.0001
   ✅ 优化器已切换到简化Hash训练模式（等价wrap版本）
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.32it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:12<00:00, 13.55it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.30it/s]
Epoch-0, seen -- Mean err: 4.75, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 2.47 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [05:45<00:00, 14.05it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-0, unseen -- Mean err: 4.55, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 6.35 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [05:04<00:00, 14.05it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:17<00:00,  2.22it/s]
Epoch-0, seen_occ -- Mean err: 16.80, Acc: 0.75, Rec : 0.87, Class and Pose  : 0.74
Validation time for epoch 0: 5.65 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:51<00:00, 13.84it/s]
100%|██████████████████████████████████████████| 623/623 [1:02:12<00:00,  5.99s/it]
Epoch-0, unseen_occ -- Mean err: 10.64, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 0: 3.19 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.32it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:14<00:00, 13.10it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.28it/s]
Epoch-1, seen -- Mean err: 4.81, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 2.52 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [05:42<00:00, 14.17it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.27it/s]
Epoch-1, unseen -- Mean err: 4.47, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 6.30 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [05:08<00:00, 13.83it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:16<00:00,  2.28it/s]
Epoch-1, seen_occ -- Mean err: 16.36, Acc: 0.75, Rec : 0.89, Class and Pose  : 0.75
Validation time for epoch 1: 5.75 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:46<00:00, 14.29it/s]
100%|██████████████████████████████████████████| 623/623 [1:02:05<00:00,  5.98s/it]
Epoch-1, unseen_occ -- Mean err: 9.73, Acc: 0.90, Rec : 0.98, Class and Pose  : 0.90
Validation time for epoch 1: 3.10 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.31it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:13<00:00, 13.43it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.30it/s]
Epoch-2, seen -- Mean err: 4.59, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 2.50 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4848/4848 [05:55<00:00, 13.64it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.30it/s]
Epoch-2, unseen -- Mean err: 4.38, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 2: 6.51 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [05:04<00:00, 14.05it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:16<00:00,  2.24it/s]
Epoch-2, seen_occ -- Mean err: 17.12, Acc: 0.74, Rec : 0.86, Class and Pose  : 0.73
Validation time for epoch 2: 5.66 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 2377/2377 [02:46<00:00, 14.27it/s]
 70%|██████████████████████████████▊             | 436/623 [43:26<18:37,  5.98s/it]
Epoch-2, unseen_occ -- Mean err: 9.95, Acc: 0.90, Rec : 0.98, Class and Pose  : 0.90
Validation time for epoch 2: 3.10 minutes
✅ Hash编码器已设置为训练模式
 12%|████▊                                   | 3/25 [4:43:49<34:41:20, 5676.37s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True
Traceback (most recent call last):
  File "train_new.py", line 437, in <module>
    else:
  File "train_new.py", line 322, in main
    # 简化版：统一应用学习率衰减
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 129, in train
    template_features = model(template)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 1372, in forward
    sam_cls_tokens, sam_patch_tokens = self.extract_sam_features(x, sam_layer_indices)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 1321, in extract_sam_features
    _ = self.sam_encoder.image_encoder(x_resized)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 254, in forward
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 138, in forward
    x = x + self.attn(self.norm1(x))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 85, in forward
    x = self.proj(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/linear.py", line 114, in forward
    return F.linear(input, self.weight, self.bias)
KeyboardInterrupt
