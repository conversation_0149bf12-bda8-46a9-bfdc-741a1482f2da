特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [17:57<00:00,  1.73s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:19<00:00,  8.50it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:56<00:00, 17.30it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.46it/s]
Epoch-0, seen -- Mean err: 5.21, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 1.33 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [04:08<00:00, 19.53it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.38it/s]
Epoch-0, unseen -- Mean err: 4.70, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 4.36 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [03:42<00:00, 19.22it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  6.45it/s]
Epoch-0, seen_occ -- Mean err: 14.51, Acc: 0.82, Rec : 0.93, Class and Pose  : 0.82
Validation time for epoch 0: 3.94 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [01:41<00:00, 23.44it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [17:21<00:00,  1.67s/it]
Epoch-0, unseen_occ -- Mean err: 10.86, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.85
Validation time for epoch 0: 1.84 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:18<00:00,  9.01it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:39<00:00, 24.89it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:08<00:00,  9.01it/s]
Epoch-1, seen -- Mean err: 5.27, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 1.02 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [02:43<00:00, 29.68it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:08<00:00,  9.16it/s]
Epoch-1, unseen -- Mean err: 5.17, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 2.90 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [02:47<00:00, 25.49it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  7.51it/s]
Epoch-1, seen_occ -- Mean err: 17.95, Acc: 0.76, Rec : 0.88, Class and Pose  : 0.75
Validation time for epoch 1: 2.96 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [01:19<00:00, 30.08it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [16:38<00:00,  1.60s/it]
Epoch-1, unseen_occ -- Mean err: 15.43, Acc: 0.80, Rec : 0.96, Class and Pose  : 0.80
Validation time for epoch 1: 1.44 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:18<00:00,  9.05it/s]4s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:39<00:00, 24.84it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:08<00:00,  8.92it/s]
Epoch-2, seen -- Mean err: 5.35, Acc: 0.99, Rec : 0.99, Class and Pose  : 0.98
Validation time for epoch 2: 1.01 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [02:37<00:00, 30.69it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:08<00:00,  9.11it/s]
Epoch-2, unseen -- Mean err: 5.37, Acc: 0.97, Rec : 1.00, Class and Pose  : 0.97
Validation time for epoch 2: 2.81 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [02:18<00:00, 30.75it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:04<00:00,  8.29it/s]
Epoch-2, seen_occ -- Mean err: 19.03, Acc: 0.74, Rec : 0.86, Class and Pose  : 0.73
Validation time for epoch 2: 2.48 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [01:15<00:00, 31.42it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [16:41<00:00,  1.61s/it]
Epoch-2, unseen_occ -- Mean err: 16.21, Acc: 0.78, Rec : 0.96, Class and Pose  : 0.77
Validation time for epoch 2: 1.37 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:19<00:00,  8.68it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:46<00:00, 20.92it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  8.16it/s]
Epoch-3, seen -- Mean err: 5.05, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 1.15 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [03:17<00:00, 24.57it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  8.40it/s]
Epoch-3, unseen -- Mean err: 5.19, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 3: 3.49 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [02:41<00:00, 26.42it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  7.28it/s]
Epoch-3, seen_occ -- Mean err: 19.01, Acc: 0.74, Rec : 0.86, Class and Pose  : 0.74
Validation time for epoch 3: 2.88 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [01:39<00:00, 23.84it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [17:10<00:00,  1.65s/it]
Epoch-3, unseen_occ -- Mean err: 15.37, Acc: 0.80, Rec : 0.95, Class and Pose  : 0.80
Validation time for epoch 3: 1.79 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:19<00:00,  8.78it/s]3s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:46<00:00, 21.29it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  7.68it/s]
Epoch-4, seen -- Mean err: 4.79, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 1.13 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [03:11<00:00, 25.29it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  8.34it/s]
Epoch-4, unseen -- Mean err: 5.18, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 4: 3.41 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [03:07<00:00, 22.84it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  7.09it/s]
Epoch-4, seen_occ -- Mean err: 18.70, Acc: 0.74, Rec : 0.87, Class and Pose  : 0.74
Validation time for epoch 4: 3.32 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [01:35<00:00, 24.78it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [16:58<00:00,  1.64s/it]
Epoch-4, unseen_occ -- Mean err: 14.23, Acc: 0.81, Rec : 0.96, Class and Pose  : 0.81
Validation time for epoch 4: 1.75 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:19<00:00,  8.51it/s]9s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:51<00:00, 19.05it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  7.67it/s]
Epoch-5, seen -- Mean err: 4.73, Acc: 1.00, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 5: 1.23 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [03:39<00:00, 22.07it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  8.18it/s]
Epoch-5, unseen -- Mean err: 5.10, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 5: 3.87 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [02:47<00:00, 25.57it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:04<00:00,  8.08it/s]
Epoch-5, seen_occ -- Mean err: 17.88, Acc: 0.76, Rec : 0.87, Class and Pose  : 0.75
Validation time for epoch 5: 2.99 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [01:24<00:00, 27.98it/s]
 24%|██████████████████████████████▏                                                                                               | 6/25 [2:39:28<8:23:00, 1588.46s/it]
Epoch-5, unseen_occ -- Mean err: 13.83, Acc: 0.82, Rec : 0.96, Class and Pose  : 0.82
Validation time for epoch 5: 1.53 minutes
 95%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▌      | 592/623 [16:06<00:50,  1.61s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
