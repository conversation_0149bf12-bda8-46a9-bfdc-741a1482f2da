{"time":"2025-07-29T22:40:08.776495711+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250729_224008-gcqngdsd/logs/debug-core.log"}
{"time":"2025-07-29T22:40:08.886560177+08:00","level":"INFO","msg":"created new stream","id":"gcqngdsd"}
{"time":"2025-07-29T22:40:08.886632117+08:00","level":"INFO","msg":"stream: started","id":"gcqngdsd"}
{"time":"2025-07-29T22:40:08.886666246+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"gcqngdsd"}
{"time":"2025-07-29T22:40:08.886854422+08:00","level":"INFO","msg":"handler: started","stream_id":"gcqngdsd"}
{"time":"2025-07-29T22:40:08.886879205+08:00","level":"INFO","msg":"sender: started","stream_id":"gcqngdsd"}
{"time":"2025-07-29T22:40:09.620644493+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-29T22:47:15.761069666+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-29T23:34:30.763182369+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-29T23:50:45.760933558+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-30T00:01:00.761652429+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-30T02:17:45.760104041+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-30T03:27:27.220052381+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-30T03:29:42.220952194+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-30T03:55:04.858707972+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-30T05:18:49.86514753+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-30T05:29:34.859100893+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-30T07:57:15.056830362+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": local error: tls: bad record MAC"}
{"time":"2025-07-30T09:45:14.729840653+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-30T09:45:22.143288816+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-30T09:45:31.638906073+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-30T09:45:45.616515307+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-729/gcqngdsd/file_stream\": EOF"}
{"time":"2025-07-30T09:45:57.580343484+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-30T09:46:12.796804856+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": local error: tls: bad record MAC"}
{"time":"2025-07-30T11:08:28.992143456+08:00","level":"INFO","msg":"stream: closing","id":"gcqngdsd"}
{"time":"2025-07-30T11:08:28.992195765+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-30T11:08:28.993009439+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-30T11:08:30.272778819+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-30T11:08:31.572527364+08:00","level":"INFO","msg":"handler: closed","stream_id":"gcqngdsd"}
{"time":"2025-07-30T11:08:31.572549518+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"gcqngdsd"}
{"time":"2025-07-30T11:08:31.572577913+08:00","level":"INFO","msg":"sender: closed","stream_id":"gcqngdsd"}
{"time":"2025-07-30T11:08:31.57262325+08:00","level":"INFO","msg":"stream: closed","id":"gcqngdsd"}
