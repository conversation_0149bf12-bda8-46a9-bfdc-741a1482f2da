特征提取器可训练参数数量: 3.21M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 3.21M
100%|██████████████████████████████████████████| 621/621 [1:00:58<00:00,  5.89s/it]
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.31it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:55<00:00, 17.55it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.26it/s]
Epoch-0, seen -- Mean err: 4.89, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 2.20 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [04:19<00:00, 18.75it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-0, unseen -- Mean err: 4.73, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 4.93 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [03:26<00:00, 19.71it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-0, seen_occ -- Mean err: 9.93, Acc: 0.86, Rec : 0.94, Class and Pose  : 0.86
Validation time for epoch 0: 4.03 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [03:39<00:00, 19.44it/s]
100%|██████████████████████████████████████████| 621/621 [1:00:26<00:00,  5.84s/it]
Epoch-0, unseen_occ -- Mean err: 14.07, Acc: 0.82, Rec : 0.93, Class and Pose  : 0.82
Validation time for epoch 0: 4.24 minutes
处理模板数据:   1%|▎                     | 2/170 [10:11:19<855:51:47, 18339.93s/it]5s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
  4%|█▍                                   | 1/25 [12:28:17<299:18:58, 44897.44s/it]
Traceback (most recent call last):
  File "train_new.py", line 443, in <module>
    main()
  File "train_new.py", line 363, in main
    testing_score = testing_utils_dino.test(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 87, in test
    for miniBatch in tqdm(template_dataloader, desc="处理模板数据"):
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/std.py", line 1181, in __iter__
    for obj in iterable:
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 681, in __next__
    data = self._next_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1359, in _next_data
    idx, data = self._get_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1315, in _get_data
    success, data = self._try_get_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1163, in _try_get_data
    data = self._data_queue.get(timeout=timeout)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/queue.py", line 179, in get
    self.not_empty.wait(remaining)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/threading.py", line 306, in wait
    gotit = waiter.acquire(True, timeout)
KeyboardInterrupt
