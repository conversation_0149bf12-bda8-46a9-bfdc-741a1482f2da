import os
import sys
import time
from PIL import Image
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.datasets.linemod import inout, processing_utils
from lib.utils.config import Config
from tqdm import tqdm

def generate_masks_for_object(idx_obj, object_name, config):
    """为指定物体生成缺失的mask文件"""
    print(f"Processing {object_name} (idx_obj={idx_obj})...")
    
    # 路径设置
    root_path = config.root_path
    crop_size = 512
    dataset = "occlusionLINEMOD"
    
    # 原始图像目录
    img_obj_dir = os.path.join(root_path, "linemod", dataset, "RGB-D/rgb_noseg")
    
    # crop后的保存目录
    save_obj_crop_dir = os.path.join(root_path, f"crop_image{crop_size}", dataset, object_name)
    
    if not os.path.exists(save_obj_crop_dir):
        print(f"Directory {save_obj_crop_dir} does not exist!")
        return
    
    # 获取所有图像文件
    num_images = len(os.listdir(img_obj_dir))
    img_name = "color_{:05d}.png"
    
    generated_count = 0
    skipped_count = 0
    
    for idx_frame in tqdm(range(num_images), desc=f"Processing {object_name}"):
        # 检查是否已经有对应的crop图像
        crop_img_path = os.path.join(save_obj_crop_dir, f"{idx_frame:06d}.png")
        crop_mask_path = os.path.join(save_obj_crop_dir, f"{idx_frame:06d}_mask.png")
        
        # 如果crop图像存在但mask不存在，则生成mask
        if os.path.exists(crop_img_path) and not os.path.exists(crop_mask_path):
            # 读取原始图像
            original_img_path = os.path.join(img_obj_dir, img_name.format(idx_frame))
            if not os.path.exists(original_img_path):
                continue
                
            # 读取opencv pose
            opencv_pose = inout.read_opencv_pose_linemod(
                root_dir=root_path, 
                dataset=dataset,
                idx_obj=idx_obj, 
                id_frame=idx_frame, 
                split=None
            )
            
            if opencv_pose is not None:
                # 检查mask文件是否存在
                mask_path = f"dataset/lmo/test/000002/mask_visib/{idx_frame:06d}_{idx_obj:06d}.png"
                if os.path.exists(mask_path):
                    try:
                        # 读取mask
                        mask = Image.open(mask_path)
                        
                        # 使用相同的crop逻辑处理mask
                        crop_mask = processing_utils.crop_frame(
                            opencv_pose, 
                            img=mask, 
                            crop_size=crop_size,
                            virtual_bbox_size=0.2, 
                            ignore_inplane=False
                        )
                        
                        # 保存crop后的mask
                        crop_mask.save(crop_mask_path)
                        generated_count += 1
                        
                    except Exception as e:
                        print(f"Error processing frame {idx_frame}: {e}")
                else:
                    print(f"Mask file not found: {mask_path}")
            else:
                print(f'opencv_pose is None for frame {idx_frame}, obj {idx_obj}')
        else:
            skipped_count += 1
    
    print(f"Completed {object_name}: Generated {generated_count} masks, Skipped {skipped_count}")

if __name__ == '__main__':
    parser = argparse.ArgumentParser('Generate missing masks for glue and holepuncher')
    parser.add_argument('--config', type=str, default="./config.json")
    parser.add_argument('--objects', type=str, nargs='+', default=['glue', 'holepuncher'],
                       help='Objects to process (default: glue holepuncher)')
    args = parser.parse_args()

    config = Config(config_file=args.config).get_config()
    
    # 物体名称到索引的映射
    object_to_idx = {
        'glue': 6,
        'holepuncher': 7
    }
    
    start_time = time.time()
    
    for object_name in args.objects:
        if object_name in object_to_idx:
            idx_obj = object_to_idx[object_name]
            generate_masks_for_object(idx_obj, object_name, config)
        else:
            print(f"Unknown object: {object_name}")
    
    finish_time = time.time()
    print(f"Total time: {finish_time - start_time:.2f} seconds")
