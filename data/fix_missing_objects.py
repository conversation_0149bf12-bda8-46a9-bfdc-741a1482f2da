#!/usr/bin/env python3

import sys
import os
import json
import glob
import numpy as np
from tqdm import tqdm

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.datasets.linemod import inout
from lib.poses import utils
from lib.utils.config import Config

def find_best_template(query_pose, template_poses, id_symmetry, base_on_angle=False):
    """找到最佳模板匹配"""
    if base_on_angle:
        query_pose = query_pose[2, :3]
        template_poses = template_poses[:, 2, :3]
    else:
        query_pose = query_pose[2, :3]
        template_poses = template_poses[:, 2, :3]
    
    # 计算角度差异
    division_term = np.linalg.norm(query_pose) * np.linalg.norm(template_poses, axis=1)
    delta = np.clip(np.dot(template_poses, query_pose) / division_term, a_min=0, a_max=1)
    delta_degree = np.rad2deg(np.arccos(delta))
    
    if id_symmetry != 0:
        # 处理对称物体
        delta_degree = np.minimum(delta_degree, 180 - delta_degree)
    
    idx_best = np.argmin(delta_degree)
    return idx_best, delta_degree[idx_best]

def process_missing_objects():
    """处理缺失的glue和holepuncher数据"""
    print("=== 处理缺失的glue和holepuncher数据 ===")
    
    # 加载配置
    config = Config(config_file="config.json").get_config()
    
    # 加载模板poses
    test_template_poses = np.load("lib/poses/predefined_poses/half_sphere_level2.npy")
    train_template_poses = np.load("lib/poses/predefined_poses/half_sphere_level2_and_level3.npy")
    
    # 加载现有的JSON数据
    json_path = "dataset/occlusionLINEMOD.json"
    if os.path.exists(json_path):
        with open(json_path) as f:
            existing_data = json.load(f)
        print(f"加载现有数据，包含 {len(existing_data['id_obj'])} 条记录")
    else:
        existing_data = {
            'id_obj': [],
            'real_path': [],
            'real_location': [],
            'test_template_path': [],
            'test_template_location': [],
            'train_template_path': [],
            'train_template_location': []
        }
        print("创建新的数据结构")
    
    # 处理索引6和7（glue和holepuncher）
    missing_indices = [6, 7]  # 对应glue和holepuncher
    
    for idx_obj in missing_indices:
        id_obj = int(inout.occlusion_real_ids[idx_obj])
        obj_name = inout.occlusion_LINEMOD_names[idx_obj]
        
        print(f"\n处理物体: {obj_name} (ID: {id_obj}, 索引: {idx_obj})")
        
        # 检查图像文件
        dataset_path = "linemod/occlusionLINEMOD/RGB-D/rgb_noseg/"
        image_pattern = os.path.join(config.root_path, dataset_path, "*.png")
        all_images = glob.glob(image_pattern)
        num_real_frames = len(all_images)
        print(f"总图像数量: {num_real_frames}")
        
        # 检查crop图像
        crop_dir = f"dataset/crop_image512/occlusionLINEMOD/{obj_name}/"
        if not os.path.exists(crop_dir):
            print(f"ERROR: Crop目录不存在: {crop_dir}")
            continue
            
        crop_images = [f for f in os.listdir(crop_dir) if f.endswith('.png') and not f.endswith('_mask.png')]
        print(f"Crop图像数量: {len(crop_images)}")
        
        processed_count = 0
        
        for id_frame in tqdm(range(num_real_frames), desc=f"处理{obj_name}"):
            # 检查pose数据
            pose = inout.read_opencv_pose_linemod(
                root_dir=config.root_path,
                dataset="occlusionLINEMOD",
                idx_obj=idx_obj, 
                id_frame=id_frame
            )
            
            if pose is not None:
                # 检查对应的crop图像是否存在
                crop_img_path = f"{id_frame:06d}.png"
                crop_mask_path = f"{id_frame:06d}_mask.png"
                
                if (os.path.exists(os.path.join(crop_dir, crop_img_path)) and 
                    os.path.exists(os.path.join(crop_dir, crop_mask_path))):
                    
                    # 处理pose
                    pose = utils.remove_inplane_rotation(pose)
                    pose = utils.opencv2opengl(pose)
                    
                    # 找到最佳模板
                    idx_test_template, test_error = find_best_template(
                        query_pose=np.copy(pose),
                        template_poses=np.copy(test_template_poses),
                        id_symmetry=inout.list_id_symmetry[idx_obj],
                        base_on_angle=False
                    )
                    
                    idx_train_template, train_error = find_best_template(
                        query_pose=np.copy(pose),
                        template_poses=np.copy(train_template_poses),
                        id_symmetry=inout.list_id_symmetry[idx_obj],
                        base_on_angle=False
                    )
                    
                    # 添加到数据中
                    existing_data['id_obj'].append(id_obj)
                    existing_data['real_path'].append(f"{obj_name}/{crop_img_path}")
                    existing_data['real_location'].append(pose[2, :3].tolist())
                    existing_data['test_template_path'].append(f"templatesLINEMOD/{obj_name}/{idx_test_template:06d}.png")
                    existing_data['test_template_location'].append(test_template_poses[idx_test_template][2, :3].tolist())
                    existing_data['train_template_path'].append(f"templatesLINEMOD/{obj_name}/{idx_train_template:06d}.png")
                    existing_data['train_template_location'].append(train_template_poses[idx_train_template][2, :3].tolist())
                    
                    processed_count += 1
        
        print(f"成功处理 {processed_count} 条 {obj_name} 数据")
    
    # 保存更新后的JSON文件
    print(f"\n保存更新后的数据，总计 {len(existing_data['id_obj'])} 条记录")
    with open(json_path, 'w') as f:
        json.dump(existing_data, f, indent=2)
    
    print("数据更新完成！")
    
    # 验证结果
    print("\n验证结果:")
    unique_ids = sorted(list(set(existing_data['id_obj'])))
    print(f"包含的物体ID: {unique_ids}")
    
    for obj_id in [10, 11]:
        count = existing_data['id_obj'].count(obj_id)
        print(f"ID {obj_id}: {count} 条记录")

if __name__ == "__main__":
    process_missing_objects()
