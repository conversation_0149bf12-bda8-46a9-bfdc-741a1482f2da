#!/usr/bin/env python3
"""
Quick test to verify CrossModalDINO_SAM_FeatureExtractor has extract_sam_features method
"""

import torch
from lib.utils.config import Config
from lib.models.dino_network import DINOv2Extractor
from lib.models.simple_dino_feature_network import create_adaptive_feature_extractor

def test_cross_modal_method():
    """测试CrossModalDINO_SAM_FeatureExtractor是否有extract_sam_features方法"""
    print("🧪 Testing CrossModalDINO_SAM_FeatureExtractor method existence...")
    
    # 加载配置
    config_path = "config_run/LM_DINO_split1.json"
    config = Config(config_path)
    
    print(f"📋 Configuration loaded from: {config_path}")
    print(f"   🔧 SAM enabled: {config.model.efficient_sam.enabled}")
    print(f"   🔧 Cross-Modal enabled: {config.model.efficient_sam.cross_modal}")
    
    # 初始化DINO提取器
    print("\n🔧 Initializing DINO extractor...")
    dino_extractor = DINOv2Extractor(
        device="cuda",
        output_resolution=config.model.output_resolution,
        pose_tokens_config=config.model.pose_tokens,
        feature_blocks_config=config.model.feature_blocks,
        version=config.model.version
    )
    
    # 创建Cross-Modal特征提取器
    print("\n🔧 Creating Cross-Modal feature extractor...")
    model = create_adaptive_feature_extractor(config, dino_extractor)
    
    # 检查方法是否存在
    print("\n🔍 Checking methods...")
    print(f"   ✅ Has extract_sam_features: {hasattr(model.base_extractor, 'extract_sam_features')}")
    print(f"   ✅ Has forward: {hasattr(model.base_extractor, 'forward')}")
    print(f"   ✅ Has aggregation_network: {hasattr(model.base_extractor, 'aggregation_network')}")
    
    # 检查类型
    print(f"\n📊 Model type: {type(model)}")
    print(f"📊 Base extractor type: {type(model.base_extractor)}")
    
    if hasattr(model.base_extractor, 'extract_sam_features'):
        print("✅ extract_sam_features method found!")
        return True
    else:
        print("❌ extract_sam_features method NOT found!")
        return False

if __name__ == "__main__":
    try:
        success = test_cross_modal_method()
        if success:
            print("\n🎉 Method test passed!")
        else:
            print("\n❌ Method test failed!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
