import os
from torch.utils.data import DataLoader
import torch


def custom_collate_fn(batch):
    """
    自定义collate函数，处理预提取特征字典和普通张量的混合数据
    Args:
        batch: 包含字典和张量混合数据的批次列表
    Returns:
        处理后的批次字典
    """
    # 检查批次中是否包含特征字典
    first_item = batch[0]
    
    # 收集所有键
    all_keys = set()
    for item in batch:
        all_keys.update(item.keys())
    
    result = {}
    
    for key in all_keys:
        values = []
        for item in batch:
            if key in item:
                values.append(item[key])
        
        if not values:
            continue
            
        # 检查第一个值的类型来决定如何处理
        first_value = values[0]
        
        if isinstance(first_value, dict):
            # 如果是特征字典（预提取特征），保持为列表形式
            result[key] = values
        elif isinstance(first_value, torch.Tensor):
            # 如果是张量，使用默认堆叠
            try:
                result[key] = torch.stack(values)
            except:
                # 如果堆叠失败，保持为列表
                result[key] = values
        elif isinstance(first_value, list):
            # 如果是列表（如pose_negatives），需要特殊处理
            if key == "pose_negatives":
                # pose_negatives: 每个样本都是一个列表，需要保持batch结构
                # 输入: [样本1的列表, 样本2的列表, ...] 
                # 输出: [样本1的列表, 样本2的列表, ...] (保持原结构)
                result[key] = values
            else:
                # 其他列表类型，保持原有逻辑
                try:
                    # 检查列表内容是否为张量
                    if values and len(values[0]) > 0 and isinstance(values[0][0], torch.Tensor):
                        # 对于其他嵌套列表
                        result[key] = values
                    else:
                        result[key] = values
                except:
                    result[key] = values
        else:
            # 对于其他类型（如数字、字符串），直接使用默认collate
            try:
                result[key] = torch.utils.data.dataloader.default_collate(values)
            except:
                result[key] = values
    
    return result


def init_dataloader(dict_dataloader, batch_size, num_workers):
    """
    初始化数据加载器
    Args:
        dict_dataloader: 数据集字典
        batch_size: batch大小
        num_workers: 工作进程数
    """
    for key in dict_dataloader.keys():
        if key == "train" or "template" in key:  # 训练集和模板数据集使用指定的batch_size
            _batch_size = batch_size
        else:  # 其他数据集使用batch_size=1
            _batch_size = 1
            
        dict_dataloader[key] = torch.utils.data.DataLoader(
            dict_dataloader[key],
            batch_size=_batch_size,
            shuffle=True if key == "train" else False,  # 只有训练集需要shuffle
            num_workers=num_workers,
            pin_memory=True,
            collate_fn=custom_collate_fn  # 使用自定义collate函数
        )
    return dict_dataloader


def write_txt(path, list_files):
    with open(path, "w") as f:
        for idx in list_files:
            f.write(idx + "\n")
        f.close()


def get_list_background_img_from_dir(background_dir):
    if not os.path.exists(os.path.join(background_dir, "list_img.txt")):
        jpgs = [os.path.join(root, file) for root, dirs, files in os.walk(background_dir)
                for file in files if file.endswith('.jpg')]
        write_txt(os.path.join(background_dir, "list_img.txt"), jpgs)
    else:
        with open(os.path.join(background_dir, "list_img.txt"), 'r') as f:
            jpgs = [x.strip() for x in f.readlines()]
    return jpgs


def sampling_k_samples(group, k=109):
    if len(group) < k:
        return group
    return group.sample(k, random_state=2024)