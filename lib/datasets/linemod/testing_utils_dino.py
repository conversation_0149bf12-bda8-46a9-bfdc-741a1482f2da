import os, time
from tqdm import tqdm
import numpy as np
import torch
import torch.nn.functional as F

from lib.utils.metrics import AverageValueMeter
from lib.datasets.linemod import inout
from lib.datasets.linemod.visualization import visualize_result
from lib.utils.similarity import calculate_similarity as unified_calculate_similarity


def calculate_score(pred_location, gt_location, id_symmetry, id_obj, pred_id_obj):
    unique_ids, inverse_indices = torch.unique(id_obj, sorted=True, return_inverse=True)
    cosine_sim = F.cosine_similarity(pred_location, gt_location)
    angle_err = torch.rad2deg(torch.arccos(cosine_sim.clamp(min=-1, max=1)))

    # for symmetry
    gt_location_opposite = gt_location.clone()  # 使用clone避免修改原始数据
    gt_location_opposite[:, :2] *= -1  # rotation 180 in Z axis
    cosine_sim_sym = F.cosine_similarity(pred_location, gt_location_opposite)  # 修正：计算与预测位置的相似度
    angle_err_sym = torch.rad2deg(torch.arccos(cosine_sim_sym.clamp(min=-1, max=1)))
    angle_err[id_symmetry == 1] = torch.minimum(angle_err[id_symmetry == 1], angle_err_sym[id_symmetry == 1])

    list_err, list_pose_acc, list_class_acc, list_class_and_pose_acc15 = {}, {}, {}, {}
    for i in range(len(unique_ids)):
        err = angle_err[id_obj == unique_ids[i]]
        recognition_acc = (pred_id_obj[id_obj == unique_ids[i]] == unique_ids[i])
        
        class_and_pose_acc15 = torch.logical_and(err <= 15, recognition_acc).float().mean()
        err = err.mean()
        recognition_acc = recognition_acc.float().mean()
        pose_acc = (err <= 15).float().mean()

        list_err[unique_ids[i].item()] = err
        list_pose_acc[unique_ids[i].item()] = pose_acc
        list_class_acc[unique_ids[i].item()] = recognition_acc
        list_class_and_pose_acc15[unique_ids[i].item()] = class_and_pose_acc15

    list_err["mean"] = torch.mean(angle_err)
    list_pose_acc["mean"] = (angle_err <= 15).float().mean()
    list_class_acc["mean"] = (pred_id_obj == id_obj).float().mean()
    list_class_and_pose_acc15["mean"] = torch.logical_and(angle_err <= 15, pred_id_obj == id_obj).float().mean()

    return list_err, list_pose_acc, list_class_acc, list_class_and_pose_acc15





def test(query_data, template_data, model, epoch, logger, split_name, list_id_obj,
         result_vis_path=None, vis=False, tensor2im=None, gt_bbox_known=True, offset_predictor=None, hash_encoder=None):
    start_time = time.time()
    list_id_obj.append("mean")
    meter_error = {id_obj: AverageValueMeter() for id_obj in list_id_obj}
    meter_accuracy = {id_obj: AverageValueMeter() for id_obj in list_id_obj}
    meter_recognition = {id_obj: AverageValueMeter() for id_obj in list_id_obj}
    meter_accuracy_class_and_pose = {id_obj: AverageValueMeter() for id_obj in list_id_obj}
    
    # 检查是否启用偏移量预测和Hash编码器
    use_offset_prediction = offset_predictor is not None
    use_hash_encoder = hash_encoder is not None

    # 确保Hash编码器在eval模式
    if use_hash_encoder:
        hash_encoder.pose_hash_encoder.eval()
        hash_encoder.cls_hash_encoder.eval()
        print("✅ Hash编码器已设置为eval模式")

    print(f"测试中偏移量预测状态: {'启用' if use_offset_prediction else '未启用'}")
    print(f"测试中Hash编码器状态: {'启用' if use_hash_encoder else '未启用'}")
    
    query_size, query_dataloader = len(query_data), iter(query_data)
    template_size, template_dataloader = len(template_data), iter(template_data)
    
    monitoring_text = "Epoch-{}, {} -- Mean err: {:.2f}, Acc: {:.2f}, Rec : {:.2f}, Class and Pose  : {:.2f}"
    timing_text = "Validation time for epoch {}: {:.02f} minutes"

    model.eval()
    with torch.no_grad():
        # 处理模板数据
        list_cls_feature_template, list_pose_feature_template = [], []
        list_original_pose_feature_template = []  # 用于offset prediction的原始特征
        list_synthetic_pose, list_id_obj_template = [], []
        list_img_template = []

        for miniBatch in tqdm(template_dataloader, desc="处理模板数据"):
            template = miniBatch["template"].cuda()
            template_pose = miniBatch["template_pose"].cuda()
            id_obj = miniBatch["id_obj"].cuda()

            # 提取原始特征
            original_features = model(template)

            # 根据是否启用Hash编码器选择特征
            if use_hash_encoder:
                # 使用Hash编码后的特征进行模板匹配
                features = hash_encoder.encode_features(original_features)
            else:
                # 使用原始特征
                features = original_features

            cls_feature = features['cls_feature']
            pose_feature = features['pose_feature']

            # 保存原始特征用于offset prediction
            list_original_pose_feature_template.append(original_features['pose_feature'])

            list_synthetic_pose.append(template_pose)
            list_id_obj_template.append(id_obj)
            list_cls_feature_template.append(cls_feature)
            list_pose_feature_template.append(pose_feature)

            if vis:
                list_img_template.append(tensor2im(template))

        list_cls_feature_template = torch.cat(list_cls_feature_template, dim=0)
        list_pose_feature_template = torch.cat(list_pose_feature_template, dim=0)
        list_original_pose_feature_template = torch.cat(list_original_pose_feature_template, dim=0)
        list_synthetic_pose = torch.cat(list_synthetic_pose, dim=0)
        list_id_obj_template = torch.cat(list_id_obj_template, dim=0)
        if vis:
            list_img_template = np.concatenate(list_img_template, axis=0)

        # 按照类别对特征进行分类
        cls_feature_dict = {}
        pose_feature_dict = {}
        synthetic_pose_dict = {}

        for i, id_obj in enumerate(list_id_obj_template):
            id_obj = id_obj.item()
            if id_obj not in cls_feature_dict:
                cls_feature_dict[id_obj] = []
                pose_feature_dict[id_obj] = []
                synthetic_pose_dict[id_obj] = []
            cls_feature_dict[id_obj].append(list_cls_feature_template[i])
            pose_feature_dict[id_obj].append(list_pose_feature_template[i])
            synthetic_pose_dict[id_obj].append(list_synthetic_pose[i])

        # 将每个类别的特征转换为张量
        for id_obj in cls_feature_dict.keys():
            cls_feature_dict[id_obj] = torch.stack(cls_feature_dict[id_obj])
            pose_feature_dict[id_obj] = torch.stack(pose_feature_dict[id_obj])
            synthetic_pose_dict[id_obj] = torch.stack(synthetic_pose_dict[id_obj])

        # 测试每个查询图像
        for miniBatch in tqdm(query_dataloader, desc="处理查询图像"):
            query = miniBatch["query"].cuda()
            query_pose = miniBatch["query_pose"].cuda()
            id_obj = miniBatch["id_obj"].cuda()
            id_symmetry = miniBatch["id_symmetry"].cuda()

            # 提取查询特征
            original_query_features = model(query)

            # 根据是否启用Hash编码器选择特征
            if use_hash_encoder:
                # 使用Hash编码后的特征进行查询
                feature_query = hash_encoder.encode_features(original_query_features)
            else:
                # 使用原始特征
                feature_query = original_query_features

            # 获取mask信息（如果存在）
            query_mask = None
            if 'query_mask' in miniBatch:
                query_mask = miniBatch['query_mask'].cuda()  # [B, H, W]
            
            if gt_bbox_known:
                # ======= 原有按类别限定检索逻辑，先注释掉 =======
                # pose_feature_templates = pose_feature_dict[id_obj.item()]
                # synthetic_poses = synthetic_pose_dict[id_obj.item()]
                # get best template
                # sim_score_pose = calculate_similarity(
                #     feature_query['pose_feature'], pose_feature_templates, mask=query_mask
                # )
                # weight_sim_pose, pred_index_pose = sim_score_pose.topk(k=1)
                # pred_pose = synthetic_poses[pred_index_pose.reshape(-1)]
                # pred_id_obj = id_obj  # 直接使用query的id_obj

                # ======= 新的全类别检索逻辑 =======
                # 构建全类别模板特征库
                all_templates = []
                all_poses = []
                all_ids = []
                for obj_id, feats in pose_feature_dict.items():
                    n = feats.shape[0]
                    all_templates.append(feats)  # [N, C, H, W] or [N, C]
                    all_poses.append(synthetic_pose_dict[obj_id])  # [N, ...]
                    all_ids.extend([obj_id] * n)
                all_templates = torch.cat(all_templates, dim=0)  # [M, ...]
                all_poses = torch.cat(all_poses, dim=0)          # [M, ...]
                all_ids = torch.tensor(all_ids, device=all_templates.device)  # [M]

                # 计算相似度 - 根据是否使用Hash选择相似度类型
                similarity_type = 'hamming' if use_hash_encoder else 'cosine'

                # 🔧 新功能：测试时强制使用mask引导相似度（即使训练时use_mask_pooling=False）
                # 这样可以在保持训练稳定的同时，获得更精确的测试结果
                sim_score_pose = unified_calculate_similarity(
                    feature_query['pose_feature'],
                    all_templates,
                    feature_type='pose',
                    similarity_type=similarity_type,
                    mask=query_mask  # 测试时始终使用mask（如果存在）
                )  # [B, M]

                # 找到最相似的模板
                weight_sim_pose, pred_index_pose = sim_score_pose.topk(k=1, dim=1)  # [B, 1]
                best_template_pose = all_poses[pred_index_pose.reshape(-1)]
                pred_id_obj = all_ids[pred_index_pose.reshape(-1)]

                # 如果启用偏移量预测，进行姿态精细化
                if use_offset_prediction:
                    # 获取最佳匹配的模板索引
                    best_template_idx = pred_index_pose.reshape(-1).item()

                    # 从保存的原始模板特征中获取对应的特征
                    best_template_original_feature = list_original_pose_feature_template[best_template_idx:best_template_idx+1]  # [1, D, H, W]

                    # 构造template特征字典格式，使用原始特征
                    template_features_dict = {
                        'pose_feature': best_template_original_feature,
                        'original_pose_feature': best_template_original_feature
                    }

                    predicted_offset = offset_predictor.predict_offset(
                        original_query_features,  # 使用原始查询特征
                        template_features_dict    # 构造的字典格式，使用原始特征
                    )

                    # 精细化姿态 = 模板姿态 + 预测偏移量
                    pred_pose = best_template_pose + predicted_offset
                else:
                    # 传统模式：直接使用模板姿态
                    pred_pose = best_template_pose

                # 后续评估
                err, acc, class_score, class_and_pose = calculate_score(pred_location=pred_pose,
                                                                        gt_location=query_pose,
                                                                        id_symmetry=id_symmetry,
                                                                        id_obj=id_obj,
                                                                        pred_id_obj=pred_id_obj)
            else:
                cls_negatives = miniBatch['cls_negatives']
                
                # 处理负样本特征
                if isinstance(cls_negatives, list):
                    # 预提取特征模式：cls_negatives是特征字典列表
                    other_cls_features = []
                    for negative_features in cls_negatives:
                        if use_cad_feature and cad_feature is not None:
                            neg_feature = model(negative_features, cad_feature=cad_feature)['cls_feature']
                        else:
                            neg_feature = model(negative_features)['cls_feature']
                        other_cls_features.append(neg_feature)
                    other_cls_features = torch.cat(other_cls_features, dim=0)
                else:
                    # 图像模式：cls_negatives是图像tensor
                    other_image = cls_negatives.cuda()
                    other_cls_features = model(other_image)['cls_feature']
                    
                cur_cls_feature = feature_query['cls_feature']  # [1, C]
                # 拼接当前特征和其他图像特征
                combined_features = torch.cat([cur_cls_feature, other_cls_features], dim=0)  # [(B+1), C]
                # 计算与模板特征的相似度
                cur_template_pose_features = cls_feature_dict[id_obj.item()]  # [N, C]
                sim = F.cosine_similarity(combined_features.unsqueeze(1),
                                       cur_template_pose_features.unsqueeze(0),
                                       dim=2)  # [(B+1), N]

                # 找到最相似的配对
                max_sim_values, max_pair_idx = torch.max(sim, dim=1)  # [(B+1)]
                max_pair_idx = torch.argmax(max_sim_values)  # 标量

                # 判断是否预测正确（最相似的是否为原始特征）
                is_correct = (max_pair_idx == 0)  # 如果是0，说明原始特征是最相似的


                # 如果类别预测正确，执行姿态匹配
                if is_correct:
                    pose_feature_templates = pose_feature_dict[id_obj.item()]
                    synthetic_poses = synthetic_pose_dict[id_obj.item()]
                    # get best template
                    sim_score_pose = calculate_similarity(
                        feature_query['pose_feature'], pose_feature_templates, mask=query_mask
                    )
                    weight_sim_pose, pred_index_pose = sim_score_pose.topk(k=1)
                    pred_pose = synthetic_poses[pred_index_pose.reshape(-1)]
                    pred_id_obj = id_obj  # 直接使用query的id_obj
                    err, acc, class_score, class_and_pose = calculate_score(pred_location=pred_pose,
                                                                            gt_location=query_pose,
                                                                            id_symmetry=id_symmetry,
                                                                            id_obj=id_obj,
                                                                            pred_id_obj=pred_id_obj)
                # 如果类别预测错误，直接视为类别预测错误，不再进行姿态匹配
                else:
                    class_score={id_obj.item():torch.tensor(0).to(id_obj.device),'mean':torch.tensor(0).to(id_obj.device)}
                    class_and_pose={id_obj.item():torch.tensor(0).to(id_obj.device),'mean':torch.tensor(0).to(id_obj.device)}
                    err, acc = None, None
                        

            for key in class_and_pose.keys():
                if err is not None:
                    meter_error[key].update(err[key].item())
                if acc is not None:
                    meter_accuracy[key].update(acc[key].item())
                meter_recognition[key].update(class_score[key].item())
                meter_accuracy_class_and_pose[key].update(class_and_pose[key].item())
        
                if vis:
                    pred_template = list_img_template[pred_index_pose.reshape(-1).cpu().numpy()]
                    sim_m_pose = sim_score_pose[pred_index_pose.reshape(-1)]

                    for q_img, t_img, sim, id, s, p in zip(
                            tensor2im(query),
                            pred_template,
                            sim_m_pose.cpu().numpy(),
                            id_obj.cpu().numpy(),
                            weight_sim_pose.cpu().numpy(),
                            query_pose.cpu().numpy()
                    ):
                        visualize_result(
                            q_img, t_img, sim, id, s[0], p,
                            os.path.join(result_vis_path, split_name)
                        )

        scores = [meter_error, meter_accuracy, meter_recognition, meter_accuracy_class_and_pose]
        results = {}
        for idx_metric, metric_name in enumerate(["error", "accuracy", "recognition", "recognition and pose"]):
            for id_obj in list_id_obj:
                if id_obj == "mean":
                    obj_name = "mean"
                else:
                    obj_name = inout.LINEMOD_real_id_to_name[id_obj]
                key_name = "{}, {}".format(metric_name, obj_name)
                results[key_name] = scores[idx_metric][id_obj].avg
        filled_monitoring_text = monitoring_text.format(epoch, split_name,
                                                        meter_error["mean"].avg,
                                                        meter_accuracy["mean"].avg,
                                                        meter_recognition["mean"].avg,
                                                        meter_accuracy_class_and_pose["mean"].avg)
        print(filled_monitoring_text)
        print(timing_text.format(epoch, (time.time() - start_time) / 60))
    return results 