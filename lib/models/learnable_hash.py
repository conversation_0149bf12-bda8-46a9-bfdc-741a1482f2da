"""
可学习哈希模块
用于将pose feature和cls feature转换到哈希空间，提高匹配精度和效率
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
# 移除外部导入，直接在本文件中实现空间哈希


class SpatialHashEncoder(nn.Module):
    """逐点独立哈希编码器 - 为特征图的每个空间位置独立生成哈希码"""

    def __init__(self, input_dim, hash_bits=64, use_attention=False, dropout_rate=0.1):
        super().__init__()
        self.input_dim = input_dim
        self.hash_bits = hash_bits
        self.use_attention = use_attention

        # 逐点卷积（1x1卷积）- 每个空间位置独立编码
        # 这样可以完全保持空间结构，每个位置的哈希码独立计算
        hidden_dim = max(input_dim // 2, hash_bits)  # 确保中间维度不会太小

        if use_attention:
            # 使用注意力机制在通道维度进行全局建模
            self.channel_attention = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),  # [B, D, H, W] -> [B, D, 1, 1]
                nn.Conv2d(input_dim, input_dim // 4, kernel_size=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(input_dim // 4, input_dim, kernel_size=1),
                nn.Sigmoid()  # 生成通道注意力权重
            )

            # 🔧 测试版本：禁用BatchNorm和Dropout
            self.pointwise_encoder = nn.Sequential(
                nn.Conv2d(input_dim, hidden_dim, kernel_size=1),
                # nn.BatchNorm2d(hidden_dim),  # 暂时禁用
                nn.ReLU(inplace=True),
                # nn.Dropout2d(dropout_rate),  # 暂时禁用

                nn.Conv2d(hidden_dim, hash_bits, kernel_size=1),
                nn.Tanh()  # 输出[-1,1]，便于二值化
            )
        else:
            # 🔧 测试版本：禁用BatchNorm和Dropout
            # 原始的简单1x1卷积
            self.pointwise_encoder = nn.Sequential(
                nn.Conv2d(input_dim, hidden_dim, kernel_size=1),
                # nn.BatchNorm2d(hidden_dim),  # 暂时禁用
                nn.ReLU(inplace=True),
                # nn.Dropout2d(dropout_rate),  # 暂时禁用

                nn.Conv2d(hidden_dim, hash_bits, kernel_size=1),
                nn.Tanh()  # 输出[-1,1]，便于二值化
            )

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        """
        前向传播
        Args:
            x: [B, input_dim, H, W] 空间特征图
        Returns:
            hash_map: [B, hash_bits, H, W] 空间哈希图
        """
        # 维度检查
        if x.shape[1] != self.input_dim:
            raise ValueError(f"Input dimension mismatch: expected {self.input_dim}, got {x.shape[1]}")

        if self.use_attention:
            # 计算通道注意力权重
            attention_weights = self.channel_attention(x)  # [B, input_dim, 1, 1]
            # 应用注意力权重进行通道维度的全局建模
            x_attended = x * attention_weights  # [B, input_dim, H, W]
            # 使用注意力加权后的特征进行编码
            hash_map = self.pointwise_encoder(x_attended)  # [B, hash_bits, H, W]
        else:
            # 直接编码（原始方式）
            hash_map = self.pointwise_encoder(x)

        if self.training:
            return hash_map  # 训练时保持连续值，便于梯度传播
        else:
            return torch.sign(hash_map)  # 推理时二值化


class HybridHashSimilarity:
    """混合哈希相似度计算器 - 支持向量哈希和空间哈希"""

    @staticmethod
    def spatial_hamming_similarity(query_hash, template_hash):
        """
        计算空间哈希的汉明相似度
        Args:
            query_hash: [B, hash_bits, H, W] 查询空间哈希
            template_hash: [N, hash_bits, H, W] 模板空间哈希
        Returns:
            similarity: [B, N] 相似度矩阵
        """
        B, query_hash_bits, H, W = query_hash.shape
        N, template_hash_bits, template_H, template_W = template_hash.shape

        # 强制检查维度匹配
        if query_hash_bits != template_hash_bits:
            raise ValueError(f"Hash dimension mismatch: query has {query_hash_bits} bits, template has {template_hash_bits} bits. "
                           f"Both query and template must have the same hash dimension.")

        hash_bits = query_hash_bits

        # 检查空间维度匹配
        if (H, W) != (template_H, template_W):
            # 将template resize到query的空间尺寸
            template_hash = F.interpolate(template_hash, size=(H, W), mode='bilinear', align_corners=False)

        # 确保是二进制哈希码
        query_binary = torch.sign(query_hash)
        template_binary = torch.sign(template_hash)

        # 重塑为 [B, hash_bits, H*W] 和 [N, hash_bits, H*W]
        query_flat = query_binary.view(B, hash_bits, -1)  # [B, hash_bits, H*W]
        template_flat = template_binary.view(N, hash_bits, -1)  # [N, hash_bits, H*W]

        # 计算每个空间位置的汉明距离
        query_expanded = query_flat.unsqueeze(1)  # [B, 1, hash_bits, H*W]
        template_expanded = template_flat.unsqueeze(0)  # [1, N, hash_bits, H*W]

        # 计算空间汉明距离
        xor_result = query_expanded != template_expanded  # [B, N, hash_bits, H*W]
        spatial_hamming_distance = xor_result.sum(dim=2).float()  # [B, N, H*W]

        # 转换为相似度并在空间维度上平均
        spatial_similarity = 1.0 - (spatial_hamming_distance / hash_bits)  # [B, N, H*W]
        similarity = spatial_similarity.mean(dim=2)  # [B, N]

        return similarity

    @staticmethod
    def spatial_cosine_similarity(query_hash, template_hash):
        """
        计算空间哈希的余弦相似度（训练时使用）
        Args:
            query_hash: [B, hash_bits, H, W] 查询空间哈希
            template_hash: [N, hash_bits, H, W] 模板空间哈希
        Returns:
            similarity: [B, N] 相似度矩阵
        """
        B, query_hash_bits, H, W = query_hash.shape
        N, template_hash_bits, template_H, template_W = template_hash.shape

        # 强制检查维度匹配
        if query_hash_bits != template_hash_bits:
            raise ValueError(f"Hash dimension mismatch: query has {query_hash_bits} bits, template has {template_hash_bits} bits. "
                           f"Both query and template must have the same hash dimension.")

        hash_bits = query_hash_bits

        # 检查空间维度匹配
        if (H, W) != (template_H, template_W):
            # 将template resize到query的空间尺寸
            template_hash = F.interpolate(template_hash, size=(H, W), mode='bilinear', align_corners=False)

        # 重塑为 [B, hash_bits, H*W] 和 [N, hash_bits, H*W]
        query_flat = query_hash.view(B, hash_bits, -1)  # [B, hash_bits, H*W]
        template_flat = template_hash.view(N, hash_bits, -1)  # [N, hash_bits, H*W]

        # 计算每个空间位置的余弦相似度
        query_expanded = query_flat.unsqueeze(1)  # [B, 1, hash_bits, H*W]
        template_expanded = template_flat.unsqueeze(0)  # [1, N, hash_bits, H*W]

        # 计算空间余弦相似度
        spatial_similarities = F.cosine_similarity(
            query_expanded,  # [B, 1, hash_bits, H*W]
            template_expanded,  # [1, N, hash_bits, H*W]
            dim=2  # 在hash_bits维度上计算相似度
        )  # [B, N, H*W]

        # 对空间维度取平均
        similarity = spatial_similarities.mean(dim=2)  # [B, N]

        return similarity

    @staticmethod
    def adaptive_similarity(query_hash, template_hash, training=False):
        """
        自适应相似度计算：训练时用余弦相似度，推理时用汉明相似度
        支持向量哈希和空间哈希
        Args:
            query_hash: [B, hash_bits] 或 [B, hash_bits, H, W] 查询哈希
            template_hash: [N, hash_bits] 或 [N, hash_bits, H, W] 模板哈希
            training: bool, 是否在训练模式
        Returns:
            similarity: [B, N] 相似度矩阵
        """
        # 检查输入维度
        if query_hash.dim() == 2 and template_hash.dim() == 2:
            # 向量哈希
            if training:
                # 训练时用余弦相似度
                return F.cosine_similarity(
                    query_hash.unsqueeze(1),  # [B, 1, hash_bits]
                    template_hash.unsqueeze(0),  # [1, N, hash_bits]
                    dim=2
                )  # [B, N]
            else:
                # 推理时用汉明相似度
                query_binary = torch.sign(query_hash)
                template_binary = torch.sign(template_hash)

                # 计算汉明距离
                xor_result = query_binary.unsqueeze(1) != template_binary.unsqueeze(0)  # [B, N, hash_bits]
                hamming_distance = xor_result.sum(dim=2).float()  # [B, N]

                # 转换为相似度
                hash_bits = query_hash.shape[1]
                return 1.0 - (hamming_distance / hash_bits)  # [B, N]

        elif query_hash.dim() == 4 and template_hash.dim() == 4:
            # 空间哈希
            if training:
                return HybridHashSimilarity.spatial_cosine_similarity(query_hash, template_hash)
            else:
                return HybridHashSimilarity.spatial_hamming_similarity(query_hash, template_hash)

        else:
            raise ValueError(f"Incompatible hash dimensions: query {query_hash.shape}, template {template_hash.shape}")


class LearnableHashEncoder(nn.Module):
    """可学习哈希编码器"""
    
    def __init__(self, input_dim, hash_bits=64, use_attention=True, dropout_rate=0.1):
        super().__init__()
        self.input_dim = input_dim
        self.hash_bits = hash_bits
        self.use_attention = use_attention
        
        # 🔧 测试版本：禁用BatchNorm和Dropout
        # 主编码器网络
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            # nn.BatchNorm1d(input_dim // 2),  # 暂时禁用
            nn.ReLU(inplace=True),
            # nn.Dropout(dropout_rate),  # 暂时禁用

            nn.Linear(input_dim // 2, input_dim // 4),
            # nn.BatchNorm1d(input_dim // 4),  # 暂时禁用
            nn.ReLU(inplace=True),
            # nn.Dropout(dropout_rate),  # 暂时禁用

            nn.Linear(input_dim // 4, hash_bits),
            nn.Tanh()  # 输出[-1,1]，便于二值化
        )
        
        # 可选的注意力模块
        if use_attention:
            self.attention = nn.Sequential(
                nn.Linear(input_dim, input_dim // 4),
                nn.ReLU(inplace=True),
                nn.Linear(input_dim // 4, input_dim),
                nn.Sigmoid()
            )
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """
        前向传播
        Args:
            x: [B, input_dim] 输入特征
        Returns:
            hash_code: [B, hash_bits] 哈希码
        """
        # 应用注意力机制（如果启用）
        if self.use_attention:
            attention_weights = self.attention(x)
            x = x * attention_weights
        
        # 生成连续哈希码
        continuous_hash = self.encoder(x)
        
        if self.training:
            return continuous_hash  # 训练时保持连续值，便于梯度传播
        else:
            return torch.sign(continuous_hash)  # 推理时二值化


class HashSimilarityCalculator:
    """哈希相似度计算器"""
    
    @staticmethod
    def hamming_similarity(query_hash, template_hash):
        """
        计算归一化汉明相似度
        Args:
            query_hash: [B, hash_bits] 查询哈希码
            template_hash: [N, hash_bits] 模板哈希码
        Returns:
            similarity: [B, N] 相似度矩阵，范围[0,1]
        """
        B, query_hash_bits = query_hash.shape
        N, template_hash_bits = template_hash.shape

        # 强制检查维度匹配
        if query_hash_bits != template_hash_bits:
            raise ValueError(f"Vector hash dimension mismatch: query has {query_hash_bits} bits, template has {template_hash_bits} bits. "
                           f"Both query and template must have the same hash dimension.")

        hash_bits = query_hash_bits

        # 确保是二进制哈希码
        query_binary = torch.sign(query_hash)
        template_binary = torch.sign(template_hash)

        # 计算汉明距离：使用XOR操作
        # query_binary: [B, hash_bits] -> [B, 1, hash_bits]
        # template_binary: [N, hash_bits] -> [1, N, hash_bits]
        xor_result = query_binary.unsqueeze(1) != template_binary.unsqueeze(0)  # [B, N, hash_bits]
        hamming_distance = xor_result.sum(dim=2).float()  # [B, N]

        # 转换为相似度：距离越小，相似度越高
        hamming_similarity = 1.0 - (hamming_distance / hash_bits)  # [B, N], 范围[0,1]

        return hamming_similarity
    
    @staticmethod
    def cosine_similarity_in_hash_space(query_hash, template_hash):
        """
        在哈希空间中计算余弦相似度（用于训练时的连续值）
        Args:
            query_hash: [B, hash_bits] 查询哈希码
            template_hash: [N, hash_bits] 模板哈希码
        Returns:
            similarity: [B, N] 相似度矩阵
        """
        B, query_hash_bits = query_hash.shape
        N, template_hash_bits = template_hash.shape

        # 强制检查维度匹配
        if query_hash_bits != template_hash_bits:
            raise ValueError(f"Vector hash dimension mismatch: query has {query_hash_bits} bits, template has {template_hash_bits} bits. "
                           f"Both query and template must have the same hash dimension.")

        # L2归一化
        query_norm = F.normalize(query_hash, p=2, dim=1)
        template_norm = F.normalize(template_hash, p=2, dim=1)

        # 计算余弦相似度
        similarity = torch.matmul(query_norm, template_norm.t())
        return similarity
    
    @staticmethod
    def adaptive_similarity(query_hash, template_hash, training=False):
        """
        自适应相似度计算：训练时用余弦相似度，推理时用汉明相似度
        Args:
            query_hash: [B, hash_bits] 查询哈希码
            template_hash: [N, hash_bits] 模板哈希码
            training: bool, 是否在训练模式
        Returns:
            similarity: [B, N] 相似度矩阵
        """
        if training:
            return HashSimilarityCalculator.cosine_similarity_in_hash_space(query_hash, template_hash)
        else:
            return HashSimilarityCalculator.hamming_similarity(query_hash, template_hash)


class HashLoss(nn.Module):
    """哈希训练损失函数"""
    
    def __init__(self, quantization_weight=0.1, balance_weight=0.01):
        super().__init__()
        self.quantization_weight = quantization_weight
        self.balance_weight = balance_weight
    
    def forward(self, hash_codes, targets=None):
        """
        计算哈希损失 - 支持向量和空间哈希
        Args:
            hash_codes: [B, hash_bits] 向量哈希 或 [B, hash_bits, H, W] 空间哈希
            targets: 可选的目标标签（用于监督学习）
        Returns:
            loss_dict: 包含各种损失的字典
        """
        losses = {}

        # 1. 量化损失：鼓励哈希码接近±1
        quantization_loss = torch.mean((torch.abs(hash_codes) - 1) ** 2)
        losses['quantization_loss'] = quantization_loss

        # 2. 平衡损失：鼓励每个bit位的激活平衡
        if hash_codes.dim() == 2:
            # 向量哈希: [B, hash_bits]
            bit_balance = torch.mean(hash_codes, dim=0)  # [hash_bits]
        elif hash_codes.dim() == 4:
            # 空间哈希: [B, hash_bits, H, W]
            bit_balance = torch.mean(hash_codes, dim=(0, 2, 3))  # [hash_bits]
        else:
            raise ValueError(f"Unsupported hash dimension: {hash_codes.shape}")

        balance_loss = torch.mean(bit_balance ** 2)
        losses['balance_loss'] = balance_loss

        # 3. 总损失
        total_loss = (self.quantization_weight * quantization_loss +
                     self.balance_weight * balance_loss)
        losses['hash_total_loss'] = total_loss

        return losses


class HashAugmentedFeatureExtractor(nn.Module):
    """哈希增强的特征提取器包装器"""
    
    def __init__(self, base_extractor, use_hash=False, hash_config=None):
        super().__init__()
        self.base_extractor = base_extractor
        self.use_hash = use_hash
        
        if use_hash and hash_config is not None:
            # 获取特征维度 - 尝试多种方式获取
            descriptor_size = None

            # 方法1: 从base_extractor获取
            if hasattr(base_extractor, 'descriptor_size'):
                descriptor_size = base_extractor.descriptor_size

            # 方法2: 从aggregation_network获取
            elif hasattr(base_extractor, 'aggregation_network') and hasattr(base_extractor.aggregation_network, 'descriptor_size'):
                descriptor_size = base_extractor.aggregation_network.descriptor_size

            # 方法3: 尝试通过测试前向传播获取维度
            elif hasattr(base_extractor, 'forward'):
                try:
                    # 创建一个小的测试输入来推断维度
                    test_input = torch.randn(1, 3, 224, 224)
                    if torch.cuda.is_available() and next(base_extractor.parameters()).is_cuda:
                        test_input = test_input.cuda()

                    with torch.no_grad():
                        test_output = base_extractor(test_input)
                        if isinstance(test_output, dict) and 'pose_feature' in test_output:
                            descriptor_size = test_output['pose_feature'].shape[1]  # [B, D, H, W]
                        else:
                            descriptor_size = 128  # 使用当前配置的默认值
                except Exception as e:
                    descriptor_size = 128  # 使用当前配置的默认值

            # 方法4: 从配置获取
            elif hash_config.get('input_dim'):
                descriptor_size = hash_config.get('input_dim')

            # 方法5: 使用默认值
            else:
                descriptor_size = 128  # 更新默认值为128

            # 创建专门的哈希编码器
            # pose_feature是空间特征[B, D, H, W]，使用空间哈希编码器
            self.pose_hash_encoder = SpatialHashEncoder(
                input_dim=descriptor_size,
                hash_bits=hash_config.get('hash_bits', 64),
                use_attention=hash_config.get('use_attention', True),  # 支持空间Hash的注意力机制
                dropout_rate=hash_config.get('dropout_rate', 0.1)
            )

            # cls_feature是向量特征[B, L]，使用向量哈希编码器
            self.cls_hash_encoder = LearnableHashEncoder(
                input_dim=descriptor_size,
                hash_bits=hash_config.get('hash_bits', 64),
                use_attention=hash_config.get('use_attention', True),
                dropout_rate=hash_config.get('dropout_rate', 0.1)
            )
            
            # 混合哈希相似度计算器
            self.hash_calculator = HybridHashSimilarity()
            
            # 哈希损失函数
            self.hash_loss_fn = HashLoss(
                quantization_weight=hash_config.get('quantization_weight', 0.1),
                balance_weight=hash_config.get('balance_weight', 0.01)
            )
            
            # 存储哈希配置
            self.hash_bits = hash_config.get('hash_bits', 64)
        
        # 代理属性
        if hasattr(base_extractor, 'descriptor_size'):
            self.descriptor_size = base_extractor.descriptor_size
    
    def forward(self, *args, **kwargs):
        """
        前向传播
        Args:
            *args, **kwargs: 传递给基础提取器的参数
        Returns:
            features: 特征字典，格式与原始提取器一致
        """
        # 调用基础特征提取器
        features = self.base_extractor(*args, **kwargs)

        if self.use_hash:
            original_pose_feature = features.get('pose_feature')
            original_cls_feature = features.get('cls_feature')

            if original_pose_feature is not None:
                # 直接使用空间哈希编码器（输入应该是[B, D, H, W]）
                pose_hash = self.pose_hash_encoder(original_pose_feature)
                features['pose_feature'] = pose_hash
                features['original_pose_feature'] = original_pose_feature

            if original_cls_feature is not None:
                # 使用向量哈希编码器（输入应该是[B, L]）
                cls_hash = self.cls_hash_encoder(original_cls_feature)
                features['cls_feature'] = cls_hash
                features['original_cls_feature'] = original_cls_feature



        return features

    def _process_nested_features(self, nested_features):
        """处理嵌套的特征字典"""
        processed = nested_features.copy()

        # 处理pose特征
        if 'pose_feature' in nested_features:
            original_pose = nested_features['pose_feature']
            if original_pose.dim() == 4:  # [B, D, H, W]
                pose_hash = self.pose_hash_encoder(original_pose)
                processed['pose_feature'] = pose_hash
                processed['original_pose_feature'] = original_pose

        # 处理cls特征
        if 'cls_feature' in nested_features:
            original_cls = nested_features['cls_feature']
            if original_cls.dim() == 2:  # [B, D]
                cls_hash = self.cls_hash_encoder(original_cls)
                processed['cls_feature'] = cls_hash
                processed['original_cls_feature'] = original_cls

        return processed

    # 已删除 _process_feature_for_hash 函数，因为我们只处理空间特征

    
    def calculate_similarity(self, query_features, template_features, feature_type='pose', mask=None):
        """
        计算相似度（兼容原有接口）
        Args:
            query_features: 查询特征（dict或tensor）
            template_features: 模板特征（dict或tensor）
            feature_type: 'pose' 或 'cls'
            mask: 可选的mask（保持兼容性）
        Returns:
            similarity: [B, N] 相似度矩阵
        """
        if self.use_hash:
            # 提取哈希特征
            if isinstance(query_features, dict):
                query_hash = query_features[f'{feature_type}_feature']
            else:
                query_hash = query_features

            if isinstance(template_features, dict):
                template_hash = template_features[f'{feature_type}_feature']
            else:
                template_hash = template_features

            # 强制检查维度匹配
            if len(query_hash.shape) >= 2 and len(template_hash.shape) >= 2:
                query_dim = query_hash.shape[1]
                template_dim = template_hash.shape[1]
                if query_dim != template_dim:
                    raise ValueError(f"Hash feature dimension mismatch: query has {query_dim} hash bits, template has {template_dim} hash bits. "
                                   f"Both query and template must have the same hash dimension. "
                                   f"Query shape: {query_hash.shape}, Template shape: {template_hash.shape}")

            # 哈希特征格式：pose是[B, hash_bits, H, W]，cls是[B, hash_bits]

            # 使用混合哈希相似度（自动处理向量和空间哈希）
            similarity = self.hash_calculator.adaptive_similarity(
                query_hash, template_hash, training=self.training
            )
        else:
            # 使用原始相似度计算
            similarity = self.base_extractor.calculate_similarity(
                query_features, template_features, feature_type, mask
            )

        return similarity
    
    def get_hash_loss(self, features):
        """
        获取哈希损失
        Args:
            features: 特征字典
        Returns:
            hash_losses: 哈希损失字典
        """
        if not self.use_hash:
            return {}
        
        hash_losses = {}
        
        # 计算pose特征的哈希损失
        if 'pose_feature' in features:
            pose_losses = self.hash_loss_fn(features['pose_feature'])
            for key, value in pose_losses.items():
                hash_losses[f'pose_{key}'] = value
        
        # 计算cls特征的哈希损失
        if 'cls_feature' in features:
            cls_losses = self.hash_loss_fn(features['cls_feature'])
            for key, value in cls_losses.items():
                hash_losses[f'cls_{key}'] = value
        
        return hash_losses
    
    def __getattr__(self, name):
        """代理其他属性到基础提取器"""
        try:
            return super().__getattr__(name)
        except AttributeError:
            return getattr(self.base_extractor, name)


class HashEncoder:
    """
    独立的Hash编码器
    用于训练和测试中的串联使用，不破坏特征提取器的接口一致性
    """
    def __init__(self, config, model=None):
        """
        Args:
            config: 配置对象，包含learnable_hash配置
            model: 基础特征提取器，用于获取实际特征维度
        """
        self.config = config
        hash_config = config.model.learnable_hash

        # 🔧 修复：使用与wrap版本相同的维度获取逻辑
        descriptor_size = None

        # 方法1: 从model获取（如果提供）
        if model is not None:
            if hasattr(model, 'descriptor_size'):
                descriptor_size = model.descriptor_size
            elif hasattr(model, 'aggregation_network') and hasattr(model.aggregation_network, 'descriptor_size'):
                descriptor_size = model.aggregation_network.descriptor_size

        # 方法2: 从配置获取
        if descriptor_size is None:
            descriptor_size = config.model.descriptor_size

        # 方法3: 使用默认值
        if descriptor_size is None:
            descriptor_size = 256
            print(f"⚠️  Warning: Could not determine feature dimension, using default {descriptor_size}")

        print(f"🔗 Hash encoder input dimension: {descriptor_size}")

        # 创建Hash编码器
        self.pose_hash_encoder = SpatialHashEncoder(
            input_dim=descriptor_size,
            hash_bits=hash_config.get('hash_bits', 64),
            use_attention=hash_config.get('use_attention', True),
            dropout_rate=hash_config.get('dropout_rate', 0.1)
        ).cuda()

        # 假设cls特征维度与pose特征维度相同
        self.cls_hash_encoder = LearnableHashEncoder(
            input_dim=descriptor_size,
            hash_bits=hash_config.get('hash_bits', 64),
            use_attention=hash_config.get('use_attention', True),
            dropout_rate=hash_config.get('dropout_rate', 0.1)
        ).cuda()

        # 创建Hash损失函数
        self.hash_loss_fn = HashLoss(
            quantization_weight=hash_config.get('quantization_weight', 0.1),
            balance_weight=hash_config.get('balance_weight', 0.01)
        )

        print(f"🔗 Hash Encoder initialized:")
        print(f"   📊 Hash bits: {hash_config.get('hash_bits', 64)}")
        print(f"   🎯 Use attention: {hash_config.get('use_attention', True)}")
        print(f"   📊 Descriptor size: {descriptor_size}")
        print(f"   ✅ Ready for training and testing")

    def encode_features(self, features):
        """
        将原始特征编码为Hash特征（模拟wrap版本的行为）
        Args:
            features: dict, 包含原始特征的字典
        Returns:
            hash_features: dict, 包含Hash特征和原始特征的完整字典
        """
        # 🔧 修复：复制原始字典，而不是创建新字典
        hash_features = features.copy()

        # 编码pose特征
        if 'pose_feature' in features:
            original_pose_feature = features['pose_feature']
            pose_hash = self.pose_hash_encoder(original_pose_feature)
            hash_features['pose_feature'] = pose_hash  # 替换为Hash特征
            hash_features['original_pose_feature'] = original_pose_feature  # 保存原始特征

        # 编码cls特征
        if 'cls_feature' in features:
            original_cls_feature = features['cls_feature']
            cls_hash = self.cls_hash_encoder(original_cls_feature)
            hash_features['cls_feature'] = cls_hash  # 替换为Hash特征
            hash_features['original_cls_feature'] = original_cls_feature  # 保存原始特征

        return hash_features

    def calculate_hash_loss(self, hash_features):
        """
        计算Hash损失
        Args:
            hash_features: dict, 包含Hash特征的字典
        Returns:
            hash_losses: dict, 包含各种Hash损失的字典
        """
        hash_losses = {}

        # 计算pose Hash损失
        if 'pose_feature' in hash_features:
            pose_losses = self.hash_loss_fn(hash_features['pose_feature'])
            for key, value in pose_losses.items():
                hash_losses[f'pose_{key}'] = value

        # 计算cls Hash损失
        if 'cls_feature' in hash_features:
            cls_losses = self.hash_loss_fn(hash_features['cls_feature'])
            for key, value in cls_losses.items():
                hash_losses[f'cls_{key}'] = value

        return hash_losses

    def load_state_dict(self, state_dict):
        """加载预训练权重"""
        # 提取pose hash encoder权重
        pose_hash_state_dict = {}
        cls_hash_state_dict = {}

        for key, value in state_dict.items():
            if key.startswith('pose_hash_encoder.'):
                new_key = key.replace('pose_hash_encoder.', '')
                pose_hash_state_dict[new_key] = value
            elif key.startswith('cls_hash_encoder.'):
                new_key = key.replace('cls_hash_encoder.', '')
                cls_hash_state_dict[new_key] = value

        if pose_hash_state_dict:
            self.pose_hash_encoder.load_state_dict(pose_hash_state_dict)
            print("✅ Loaded pose hash encoder weights from checkpoint")

        if cls_hash_state_dict:
            self.cls_hash_encoder.load_state_dict(cls_hash_state_dict)
            print("✅ Loaded cls hash encoder weights from checkpoint")

        if not pose_hash_state_dict and not cls_hash_state_dict:
            print("⚠️  No hash encoder weights found in checkpoint")
